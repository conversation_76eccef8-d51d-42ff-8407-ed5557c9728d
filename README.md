# نظام ERP السعودي المتكامل
## Saudi Integrated ERP System

نظام إدارة موارد المؤسسات (ERP) متكامل مصمم خصيصاً للشركات والمؤسسات السعودية مع التوافق الكامل مع متطلبات هيئة الزكاة والضرائب والجمارك (ZATCA).

A comprehensive Enterprise Resource Planning (ERP) system designed specifically for Saudi companies and organizations with full compliance with ZATCA requirements.

## 🌟 المميزات الرئيسية | Key Features

### 📊 الوحدات الأساسية | Core Modules
- **إدارة المبيعات** | Sales Management
- **إدارة المشتريات** | Procurement Management  
- **إدارة المخزون** | Inventory Management
- **إدارة الحسابات** | Accounting & Finance
- **إدارة الرواتب** | Payroll Management
- **إدارة الأصول الثابتة** | Fixed Assets Management

### 🇸🇦 التوافق مع المتطلبات السعودية | Saudi Compliance
- ✅ **ضريبة القيمة المضافة 15%** | VAT 15% Support
- ✅ **الفوترة الإلكترونية** | E-Invoicing Integration
- ✅ **تقارير هيئة الزكاة والدخل** | ZATCA Reports
- ✅ **النماذج الضريبية المعتمدة** | Approved Tax Forms
- ✅ **الربط مع نظام فاتورة** | FATOORA Integration
- ✅ **دعم الباركود والـ QR Code** | Barcode & QR Code Support

### 🌐 الواجهة والتقنيات | Interface & Technology
- 🔄 **واجهة ثنائية اللغة** (عربي/إنجليزي) | Bilingual Interface (Arabic/English)
- 📱 **تصميم متجاوب** | Responsive Design
- 🔐 **أمان متقدم** | Advanced Security
- ☁️ **نشر سحابي** | Cloud Deployment Ready

## 🛠️ التقنيات المستخدمة | Technology Stack

### Backend
- **Node.js** with TypeScript
- **Express.js** Framework
- **PostgreSQL** Database
- **Prisma** ORM
- **JWT** Authentication
- **Winston** Logging

### Frontend
- **React 18** with TypeScript
- **Vite** Build Tool
- **Tailwind CSS** Styling
- **React Query** State Management
- **React Router** Navigation
- **i18next** Internationalization

### DevOps
- **Docker** Containerization
- **Docker Compose** Orchestration
- **Nginx** Reverse Proxy
- **Redis** Caching

## 🚀 التثبيت والتشغيل | Installation & Setup

### المتطلبات الأساسية | Prerequisites
- Node.js 18+ 
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### 1. استنساخ المشروع | Clone Repository
```bash
git clone https://github.com/your-repo/saudi-erp-system.git
cd saudi-erp-system
```

### 2. إعداد متغيرات البيئة | Environment Setup
```bash
# نسخ ملف البيئة | Copy environment file
cp .env.example .env

# تحرير المتغيرات | Edit variables
nano .env
```

### 3. تشغيل بـ Docker | Run with Docker
```bash
# بناء وتشغيل الحاويات | Build and run containers
docker-compose up -d

# عرض السجلات | View logs
docker-compose logs -f
```

### 4. التثبيت اليدوي | Manual Installation

#### Backend Setup
```bash
# تثبيت التبعيات | Install dependencies
npm install

# إعداد قاعدة البيانات | Setup database
npx prisma migrate dev
npx prisma generate

# تشغيل الخادم | Start server
npm run dev
```

#### Frontend Setup
```bash
cd frontend

# تثبيت التبعيات | Install dependencies
npm install

# تشغيل التطبيق | Start application
npm run dev
```

## 📋 الاستخدام | Usage

### الوصول للنظام | System Access
- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api/docs

### حسابات التجربة | Demo Accounts
```
المدير العام | Super Admin:
Email: <EMAIL>
Password: Admin123!

المحاسب | Accountant:
Email: <EMAIL>  
Password: Account123!
```

## 📚 الوثائق | Documentation

### API Documentation
- [Authentication API](docs/api/auth.md)
- [Sales API](docs/api/sales.md)
- [VAT API](docs/api/vat.md)
- [E-Invoice API](docs/api/einvoice.md)

### User Guides
- [دليل المستخدم العربي](docs/user-guide-ar.md)
- [English User Guide](docs/user-guide-en.md)
- [إعداد الفوترة الإلكترونية](docs/einvoice-setup-ar.md)

## 🔧 التطوير | Development

### هيكل المشروع | Project Structure
```
saudi-erp-system/
├── src/                    # Backend source code
│   ├── controllers/        # Route controllers
│   ├── middleware/         # Express middleware
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── services/          # Business logic
│   └── utils/             # Utility functions
├── frontend/              # React frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   └── utils/         # Frontend utilities
├── prisma/               # Database schema
├── docs/                 # Documentation
└── docker-compose.yml    # Docker configuration
```

### أوامر التطوير | Development Commands
```bash
# Backend
npm run dev          # تشغيل الخادم | Start server
npm run build        # بناء المشروع | Build project
npm run test         # تشغيل الاختبارات | Run tests
npm run lint         # فحص الكود | Lint code

# Frontend
npm run dev          # تشغيل التطبيق | Start app
npm run build        # بناء للإنتاج | Build for production
npm run preview      # معاينة البناء | Preview build

# Database
npx prisma studio    # واجهة قاعدة البيانات | Database GUI
npx prisma migrate   # تطبيق التغييرات | Apply migrations
```

## 🧪 الاختبارات | Testing

```bash
# اختبارات Backend | Backend tests
npm run test

# اختبارات Frontend | Frontend tests
cd frontend && npm run test

# اختبارات التكامل | Integration tests
npm run test:integration
```

## 📦 النشر | Deployment

### النشر السحابي | Cloud Deployment
```bash
# AWS
docker-compose -f docker-compose.prod.yml up -d

# Azure
az container create --resource-group myResourceGroup --file docker-compose.yml

# Google Cloud
gcloud run deploy --source .
```

### النشر المحلي | Local Deployment
```bash
# بناء الإنتاج | Production build
npm run build
cd frontend && npm run build

# تشغيل الإنتاج | Start production
NODE_ENV=production npm start
```

## 🔒 الأمان | Security

- 🔐 **تشفير كلمات المرور** | Password Encryption
- 🛡️ **حماية CSRF** | CSRF Protection  
- 🔑 **مصادقة JWT** | JWT Authentication
- 🚫 **حد معدل الطلبات** | Rate Limiting
- 📝 **تسجيل العمليات** | Audit Logging

## 🤝 المساهمة | Contributing

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

We welcome contributions! Please read our [Contributing Guide](CONTRIBUTING.md) before getting started.

### خطوات المساهمة | Contribution Steps
1. Fork المشروع | Fork the project
2. إنشاء فرع جديد | Create feature branch
3. تطبيق التغييرات | Commit changes
4. دفع للفرع | Push to branch
5. إنشاء Pull Request | Create Pull Request

## 📄 الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 الدعم | Support

### التواصل | Contact
- 📧 **البريد الإلكتروني** | Email: <EMAIL>
- 💬 **الدردشة** | Chat: [Discord Server](https://discord.gg/saudi-erp)
- 📱 **واتساب** | WhatsApp: +966-XX-XXX-XXXX

### الإبلاغ عن المشاكل | Issue Reporting
- 🐛 [الإبلاغ عن خطأ | Report Bug](https://github.com/your-repo/issues/new?template=bug_report.md)
- 💡 [طلب ميزة | Feature Request](https://github.com/your-repo/issues/new?template=feature_request.md)

## 🙏 شكر وتقدير | Acknowledgments

- فريق تطوير نظام ERP السعودي | Saudi ERP Development Team
- مجتمع المطورين السعوديين | Saudi Developers Community  
- هيئة الزكاة والضرائب والجمارك | ZATCA
- مجتمع المصادر المفتوحة | Open Source Community

---

<div align="center">

**صُنع بـ ❤️ في المملكة العربية السعودية**

**Made with ❤️ in Saudi Arabia**

</div>
