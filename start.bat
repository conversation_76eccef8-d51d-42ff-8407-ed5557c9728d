@echo off
chcp 65001 >nul
cls

echo 🇸🇦 مرحباً بك في نظام ERP السعودي المتكامل
echo 🇸🇦 Welcome to Saudi Integrated ERP System
echo ==================================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js 18+ أولاً
    echo ❌ Node.js is not installed. Please install Node.js 18+ first
    echo.
    echo 📥 تحميل Node.js من: https://nodejs.org
    echo 📥 Download Node.js from: https://nodejs.org
    pause
    exit /b 1
)

REM Create necessary directories
echo 📁 إنشاء المجلدات المطلوبة...
echo 📁 Creating necessary directories...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups

REM Copy environment file if it doesn't exist
if not exist ".env" (
    echo ⚙️ إنشاء ملف البيئة...
    echo ⚙️ Creating environment file...
    copy ".env.example" ".env" >nul
    echo ✅ تم إنشاء ملف .env - يرجى تحديث الإعدادات حسب الحاجة
    echo ✅ .env file created - please update settings as needed
    echo.
)

echo اختر طريقة التشغيل ^| Choose startup method:
echo 1^) تثبيت التبعيات وإعداد المشروع ^| Install dependencies and setup
echo 2^) تشغيل Backend فقط ^| Run Backend only
echo 3^) تشغيل Frontend فقط ^| Run Frontend only
echo 4^) تشغيل النظام كاملاً ^| Run full system
echo 5^) خروج ^| Exit
echo.
set /p choice="اختيارك | Your choice (1-5): "

if "%choice%"=="1" goto setup
if "%choice%"=="2" goto backend
if "%choice%"=="3" goto frontend
if "%choice%"=="4" goto fullsystem
if "%choice%"=="5" goto exit
goto invalid

:setup
echo.
echo 🔧 إعداد المشروع...
echo 🔧 Setting up project...
echo.

echo 📦 تثبيت تبعيات Backend...
echo 📦 Installing backend dependencies...
call npm install
if errorlevel 1 (
    echo ❌ فشل في تثبيت تبعيات Backend
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت تبعيات Frontend...
echo 📦 Installing frontend dependencies...
cd frontend
call npm install
if errorlevel 1 (
    echo ❌ فشل في تثبيت تبعيات Frontend
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo.
echo ✅ تم إعداد المشروع بنجاح!
echo ✅ Project setup completed successfully!
echo.
echo 🚀 يمكنك الآن تشغيل النظام باختيار الخيار 4
echo 🚀 You can now run the system by choosing option 4
echo.
pause
goto menu

:backend
echo.
echo 🚀 تشغيل Backend...
echo 🚀 Starting backend...
echo.
echo 🌐 Backend سيعمل على: http://localhost:3000
echo 🌐 Backend will run on: http://localhost:3000
echo.
echo 🛑 لإيقاف الخادم اضغط Ctrl+C
echo 🛑 To stop server press Ctrl+C
echo.
call npm run dev
pause
goto menu

:frontend
echo.
echo 🎨 تشغيل Frontend...
echo 🎨 Starting frontend...
echo.
cd frontend
echo 🌐 Frontend سيعمل على: http://localhost:3001
echo 🌐 Frontend will run on: http://localhost:3001
echo.
echo 🛑 لإيقاف التطبيق اضغط Ctrl+C
echo 🛑 To stop application press Ctrl+C
echo.
call npm run dev
cd ..
pause
goto menu

:fullsystem
echo.
echo 🚀 تشغيل النظام كاملاً...
echo 🚀 Starting full system...
echo.

REM Start backend in background
echo 📡 تشغيل Backend...
echo 📡 Starting backend...
start "Saudi ERP Backend" cmd /k "npm run dev"

REM Wait a bit for backend to start
timeout /t 5 /nobreak >nul

REM Start frontend
echo 🎨 تشغيل Frontend...
echo 🎨 Starting frontend...
cd frontend
start "Saudi ERP Frontend" cmd /k "npm run dev"
cd ..

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo ✅ System started successfully!
echo.
echo 🌐 الروابط المتاحة ^| Available URLs:
echo    Frontend: http://localhost:3001
echo    Backend API: http://localhost:3000
echo.
echo 📝 ملاحظة: تم فتح نوافذ منفصلة للـ Backend والـ Frontend
echo 📝 Note: Separate windows opened for Backend and Frontend
echo.
pause
goto menu

:invalid
echo.
echo ❌ اختيار غير صحيح
echo ❌ Invalid choice
echo.
pause
goto menu

:exit
echo.
echo 👋 مع السلامة!
echo 👋 Goodbye!
echo.
pause
exit /b 0

:menu
cls
echo 🇸🇦 نظام ERP السعودي المتكامل
echo 🇸🇦 Saudi Integrated ERP System
echo ==================================================
goto start
