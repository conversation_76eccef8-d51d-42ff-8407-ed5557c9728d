# 🚀 الإعداد السريع لنظام ERP السعودي
## Quick Setup for Saudi ERP System

### 📋 المتطلبات الأساسية | Prerequisites

#### Windows:
- Node.js 18+ ([تحميل | Download](https://nodejs.org))
- PostgreSQL 15+ ([تحميل | Download](https://www.postgresql.org/download/windows/))
- Git ([تحميل | Download](https://git-scm.com/download/win))

#### Linux/Mac:
- Node.js 18+
- PostgreSQL 15+
- Docker & Docker Compose (اختياري | Optional)

---

## ⚡ التشغيل السريع | Quick Start

### 🪟 Windows:
```cmd
# تشغيل ملف الإعداد
start.bat
```

### 🐧 Linux/Mac:
```bash
# إعطاء صلاحيات التنفيذ
chmod +x start.sh

# تشغيل ملف الإعداد
./start.sh
```

---

## 🔧 الإعداد اليدوي | Manual Setup

### 1. استنساخ المشروع | Clone Repository
```bash
git clone <repository-url>
cd saudi-erp-system
```

### 2. إعداد Backend
```bash
# تثبيت التبعيات
npm install

# إعداد متغيرات البيئة
cp .env.example .env
# قم بتحرير ملف .env وإدخال إعدادات قاعدة البيانات

# إعداد قاعدة البيانات
npx prisma migrate dev
npx prisma generate

# تشغيل الخادم
npm run dev
```

### 3. إعداد Frontend
```bash
# الانتقال لمجلد Frontend
cd frontend

# تثبيت التبعيات
npm install

# تشغيل التطبيق
npm run dev
```

---

## 🐳 التشغيل بـ Docker

```bash
# إنشاء وتشغيل الحاويات
docker-compose up -d --build

# تطبيق تغييرات قاعدة البيانات
docker-compose exec backend npx prisma migrate deploy
docker-compose exec backend npx prisma generate
```

---

## 🌐 الوصول للنظام | System Access

- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:3000
- **Database**: localhost:5432

### 🔐 حسابات التجربة | Demo Accounts

```
المدير العام | Super Admin:
Email: <EMAIL>
Password: Admin123!

المحاسب | Accountant:
Email: <EMAIL>
Password: Account123!

مندوب المبيعات | Sales Rep:
Email: <EMAIL>
Password: Sales123!
```

---

## ⚙️ إعداد قاعدة البيانات | Database Configuration

### PostgreSQL Setup:
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE saudi_erp_db;

-- إنشاء مستخدم
CREATE USER erp_user WITH PASSWORD 'your_password';

-- إعطاء الصلاحيات
GRANT ALL PRIVILEGES ON DATABASE saudi_erp_db TO erp_user;
```

### ملف .env:
```env
DATABASE_URL="postgresql://erp_user:your_password@localhost:5432/saudi_erp_db"
JWT_SECRET="your-super-secret-jwt-key"
PORT=3000
NODE_ENV=development
```

---

## 🔍 استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues:

#### 1. خطأ في الاتصال بقاعدة البيانات
```bash
# تأكد من تشغيل PostgreSQL
# Windows:
net start postgresql-x64-15

# Linux:
sudo systemctl start postgresql

# Mac:
brew services start postgresql
```

#### 2. خطأ في المنافذ
```bash
# تحقق من المنافذ المستخدمة
# Windows:
netstat -an | findstr :3000
netstat -an | findstr :3001

# Linux/Mac:
lsof -i :3000
lsof -i :3001
```

#### 3. مشاكل npm
```bash
# مسح cache
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

---

## 📚 الوثائق | Documentation

- [دليل المستخدم العربي](docs/user-guide-ar.md)
- [English User Guide](docs/user-guide-en.md)
- [API Documentation](docs/api/README.md)
- [إعداد الفوترة الإلكترونية](docs/einvoice-setup.md)

---

## 🆘 الدعم | Support

### التواصل | Contact:
- 📧 Email: <EMAIL>
- 💬 Discord: [Saudi ERP Community](https://discord.gg/saudierp)
- 📱 WhatsApp: +966-XX-XXX-XXXX

### الإبلاغ عن المشاكل | Report Issues:
- 🐛 [Bug Report](https://github.com/your-repo/issues/new?template=bug_report.md)
- 💡 [Feature Request](https://github.com/your-repo/issues/new?template=feature_request.md)

---

## ✅ قائمة التحقق | Checklist

- [ ] تثبيت Node.js 18+
- [ ] تثبيت PostgreSQL 15+
- [ ] استنساخ المشروع
- [ ] تثبيت تبعيات Backend
- [ ] تثبيت تبعيات Frontend
- [ ] إعداد ملف .env
- [ ] إعداد قاعدة البيانات
- [ ] تشغيل Backend
- [ ] تشغيل Frontend
- [ ] اختبار تسجيل الدخول

---

<div align="center">

**🇸🇦 صُنع بـ ❤️ في المملكة العربية السعودية**

**Made with ❤️ in Saudi Arabia**

</div>
