import Joi from 'joi';
import { ValidationResult, ValidationError } from '@/types';

// Common validation schemas
export const commonSchemas = {
  id: Joi.string().uuid().required(),
  email: Joi.string().email().required(),
  phone: Joi.string().pattern(/^(\+966|0)?[5-9][0-9]{8}$/).required(),
  password: Joi.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required(),
  date: Joi.date().iso().required(),
  decimal: Joi.number().precision(2).positive(),
  vatNumber: Joi.string().pattern(/^3[0-9]{14}$/).optional(),
  commercialRegister: Joi.string().pattern(/^[0-9]{10}$/).required(),
  nationalId: Joi.string().pattern(/^[12][0-9]{9}$/).required(),
  iqama: Joi.string().pattern(/^[12][0-9]{9}$/).optional(),
};

// User validation schemas
export const userSchemas = {
  register: Joi.object({
    email: commonSchemas.email,
    username: Joi.string().alphanum().min(3).max(30).required(),
    password: commonSchemas.password,
    firstName: Joi.string().min(2).max(50).required(),
    lastName: Joi.string().min(2).max(50).required(),
    phone: commonSchemas.phone.optional(),
    companyId: commonSchemas.id.optional(),
  }),

  login: Joi.object({
    email: commonSchemas.email,
    password: Joi.string().required(),
    twoFactorCode: Joi.string().length(6).optional(),
  }),

  updateProfile: Joi.object({
    firstName: Joi.string().min(2).max(50).optional(),
    lastName: Joi.string().min(2).max(50).optional(),
    phone: commonSchemas.phone.optional(),
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: commonSchemas.password,
    confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required(),
  }),
};

// Company validation schemas
export const companySchemas = {
  create: Joi.object({
    nameAr: Joi.string().min(2).max(100).required(),
    nameEn: Joi.string().min(2).max(100).required(),
    commercialRegister: commonSchemas.commercialRegister,
    taxNumber: Joi.string().min(10).max(20).required(),
    vatNumber: commonSchemas.vatNumber,
    phone: commonSchemas.phone,
    email: commonSchemas.email,
    website: Joi.string().uri().optional(),
    addressAr: Joi.string().min(10).max(200).required(),
    addressEn: Joi.string().min(10).max(200).required(),
    city: Joi.string().min(2).max(50).required(),
    postalCode: Joi.string().pattern(/^[0-9]{5}$/).required(),
    country: Joi.string().length(2).default('SA'),
  }),

  update: Joi.object({
    nameAr: Joi.string().min(2).max(100).optional(),
    nameEn: Joi.string().min(2).max(100).optional(),
    phone: commonSchemas.phone.optional(),
    email: commonSchemas.email.optional(),
    website: Joi.string().uri().optional(),
    addressAr: Joi.string().min(10).max(200).optional(),
    addressEn: Joi.string().min(10).max(200).optional(),
    city: Joi.string().min(2).max(50).optional(),
    postalCode: Joi.string().pattern(/^[0-9]{5}$/).optional(),
  }),
};

// Customer validation schemas
export const customerSchemas = {
  create: Joi.object({
    nameAr: Joi.string().min(2).max(100).required(),
    nameEn: Joi.string().min(2).max(100).optional(),
    email: commonSchemas.email.optional(),
    phone: commonSchemas.phone,
    mobile: commonSchemas.phone.optional(),
    taxNumber: Joi.string().min(10).max(20).optional(),
    vatNumber: commonSchemas.vatNumber,
    addressAr: Joi.string().min(10).max(200).required(),
    addressEn: Joi.string().min(10).max(200).optional(),
    city: Joi.string().min(2).max(50).required(),
    postalCode: Joi.string().pattern(/^[0-9]{5}$/).optional(),
    country: Joi.string().length(2).default('SA'),
    creditLimit: commonSchemas.decimal.default(0),
    paymentTerms: Joi.number().integer().min(0).max(365).default(30),
    customerType: Joi.string().valid('INDIVIDUAL', 'COMPANY', 'GOVERNMENT').default('INDIVIDUAL'),
  }),

  update: Joi.object({
    nameAr: Joi.string().min(2).max(100).optional(),
    nameEn: Joi.string().min(2).max(100).optional(),
    email: commonSchemas.email.optional(),
    phone: commonSchemas.phone.optional(),
    mobile: commonSchemas.phone.optional(),
    taxNumber: Joi.string().min(10).max(20).optional(),
    vatNumber: commonSchemas.vatNumber,
    addressAr: Joi.string().min(10).max(200).optional(),
    addressEn: Joi.string().min(10).max(200).optional(),
    city: Joi.string().min(2).max(50).optional(),
    postalCode: Joi.string().pattern(/^[0-9]{5}$/).optional(),
    creditLimit: commonSchemas.decimal.optional(),
    paymentTerms: Joi.number().integer().min(0).max(365).optional(),
    customerType: Joi.string().valid('INDIVIDUAL', 'COMPANY', 'GOVERNMENT').optional(),
    isActive: Joi.boolean().optional(),
  }),
};

// Product validation schemas
export const productSchemas = {
  create: Joi.object({
    nameAr: Joi.string().min(2).max(100).required(),
    nameEn: Joi.string().min(2).max(100).optional(),
    description: Joi.string().max(500).optional(),
    barcode: Joi.string().max(50).optional(),
    category: Joi.string().min(2).max(50).required(),
    unit: Joi.string().min(1).max(20).required(),
    costPrice: commonSchemas.decimal.required(),
    sellingPrice: commonSchemas.decimal.required(),
    vatRate: Joi.number().min(0).max(100).default(15),
    minStock: Joi.number().integer().min(0).default(0),
    maxStock: Joi.number().integer().min(0).optional(),
    currentStock: Joi.number().integer().min(0).default(0),
    isVatExempt: Joi.boolean().default(false),
    supplierId: commonSchemas.id.optional(),
  }),

  update: Joi.object({
    nameAr: Joi.string().min(2).max(100).optional(),
    nameEn: Joi.string().min(2).max(100).optional(),
    description: Joi.string().max(500).optional(),
    barcode: Joi.string().max(50).optional(),
    category: Joi.string().min(2).max(50).optional(),
    unit: Joi.string().min(1).max(20).optional(),
    costPrice: commonSchemas.decimal.optional(),
    sellingPrice: commonSchemas.decimal.optional(),
    vatRate: Joi.number().min(0).max(100).optional(),
    minStock: Joi.number().integer().min(0).optional(),
    maxStock: Joi.number().integer().min(0).optional(),
    currentStock: Joi.number().integer().min(0).optional(),
    isVatExempt: Joi.boolean().optional(),
    isActive: Joi.boolean().optional(),
    supplierId: commonSchemas.id.optional(),
  }),
};

// Sale validation schemas
export const saleSchemas = {
  create: Joi.object({
    customerId: commonSchemas.id.required(),
    date: commonSchemas.date.default(new Date()),
    discount: commonSchemas.decimal.default(0),
    notes: Joi.string().max(500).optional(),
    items: Joi.array().items(
      Joi.object({
        productId: commonSchemas.id.required(),
        quantity: Joi.number().integer().min(1).required(),
        unitPrice: commonSchemas.decimal.required(),
      })
    ).min(1).required(),
  }),

  update: Joi.object({
    customerId: commonSchemas.id.optional(),
    date: commonSchemas.date.optional(),
    discount: commonSchemas.decimal.optional(),
    notes: Joi.string().max(500).optional(),
    status: Joi.string().valid('DRAFT', 'CONFIRMED', 'INVOICED', 'CANCELLED').optional(),
    items: Joi.array().items(
      Joi.object({
        id: commonSchemas.id.optional(),
        productId: commonSchemas.id.required(),
        quantity: Joi.number().integer().min(1).required(),
        unitPrice: commonSchemas.decimal.required(),
      })
    ).min(1).optional(),
  }),
};

// Invoice validation schemas
export const invoiceSchemas = {
  create: Joi.object({
    customerId: commonSchemas.id.required(),
    saleId: commonSchemas.id.optional(),
    date: commonSchemas.date.default(new Date()),
    dueDate: commonSchemas.date.required(),
    discount: commonSchemas.decimal.default(0),
    notes: Joi.string().max(500).optional(),
    isEInvoice: Joi.boolean().default(false),
    items: Joi.array().items(
      Joi.object({
        productId: commonSchemas.id.required(),
        description: Joi.string().min(2).max(200).required(),
        quantity: Joi.number().integer().min(1).required(),
        unitPrice: commonSchemas.decimal.required(),
      })
    ).min(1).required(),
  }),

  update: Joi.object({
    customerId: commonSchemas.id.optional(),
    date: commonSchemas.date.optional(),
    dueDate: commonSchemas.date.optional(),
    discount: commonSchemas.decimal.optional(),
    notes: Joi.string().max(500).optional(),
    status: Joi.string().valid('DRAFT', 'SENT', 'APPROVED', 'CANCELLED').optional(),
    paymentStatus: Joi.string().valid('UNPAID', 'PARTIALLY_PAID', 'PAID', 'OVERDUE').optional(),
  }),
};

// Employee validation schemas
export const employeeSchemas = {
  create: Joi.object({
    firstNameAr: Joi.string().min(2).max(50).required(),
    lastNameAr: Joi.string().min(2).max(50).required(),
    firstNameEn: Joi.string().min(2).max(50).optional(),
    lastNameEn: Joi.string().min(2).max(50).optional(),
    nationalId: commonSchemas.nationalId,
    iqamaNumber: commonSchemas.iqama,
    email: commonSchemas.email.optional(),
    phone: commonSchemas.phone,
    birthDate: commonSchemas.date.required(),
    hireDate: commonSchemas.date.required(),
    department: Joi.string().min(2).max(50).required(),
    position: Joi.string().min(2).max(50).required(),
    basicSalary: commonSchemas.decimal.required(),
    allowances: commonSchemas.decimal.default(0),
    bankAccount: Joi.string().max(50).optional(),
    iban: Joi.string().pattern(/^SA[0-9]{22}$/).optional(),
  }),

  update: Joi.object({
    firstNameAr: Joi.string().min(2).max(50).optional(),
    lastNameAr: Joi.string().min(2).max(50).optional(),
    firstNameEn: Joi.string().min(2).max(50).optional(),
    lastNameEn: Joi.string().min(2).max(50).optional(),
    email: commonSchemas.email.optional(),
    phone: commonSchemas.phone.optional(),
    department: Joi.string().min(2).max(50).optional(),
    position: Joi.string().min(2).max(50).optional(),
    basicSalary: commonSchemas.decimal.optional(),
    allowances: commonSchemas.decimal.optional(),
    bankAccount: Joi.string().max(50).optional(),
    iban: Joi.string().pattern(/^SA[0-9]{22}$/).optional(),
    isActive: Joi.boolean().optional(),
  }),
};

// Validation helper function
export const validate = (schema: Joi.ObjectSchema, data: any): ValidationResult => {
  const { error, value } = schema.validate(data, { 
    abortEarly: false,
    stripUnknown: true,
  });

  if (error) {
    const errors: ValidationError[] = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value,
    }));

    return {
      isValid: false,
      errors,
    };
  }

  return {
    isValid: true,
    errors: [],
  };
};

// Middleware for validation
export const validateRequest = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const result = validate(schema, req.body);
    
    if (!result.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: result.errors,
      });
    }

    next();
  };
};
