-- نظام ERP السعودي - بيانات أولية
-- Saudi ERP System - Initial Seed Data

-- إدراج شركة تجريبية
INSERT INTO companies (
    id,
    "nameAr",
    "nameEn", 
    "commercialRegister",
    "taxNumber",
    "vatNumber",
    phone,
    email,
    website,
    "addressAr",
    "addressEn",
    city,
    "postalCode",
    country,
    "isActive",
    "subscriptionPlan",
    "createdAt",
    "updatedAt"
) VALUES (
    'comp-demo-001',
    'شركة التجارة السعودية المحدودة',
    'Saudi Trading Company Ltd',
    '*********0',
    'TAX*********',
    '3*********01234',
    '+966112345678',
    '<EMAIL>',
    'https://sauditrade.com',
    'شارع الملك فهد، الرياض، المملكة العربية السعودية',
    '<PERSON>, Riyadh, Saudi Arabia',
    'الرياض',
    '12345',
    'SA',
    true,
    'PROFESSIONAL',
    NOW(),
    NOW()
);

-- إدراج مستخدمين تجريبيين
INSERT INTO users (
    id,
    email,
    username,
    password,
    "firstName",
    "lastName",
    phone,
    "isActive",
    role,
    "emailVerified",
    "companyId",
    "createdAt",
    "updatedAt"
) VALUES 
-- مدير عام
(
    'user-admin-001',
    '<EMAIL>',
    'admin',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL8.vKzFu', -- Admin123!
    'أحمد',
    'المدير',
    '+************',
    true,
    'ADMIN',
    true,
    'comp-demo-001',
    NOW(),
    NOW()
),
-- محاسب
(
    'user-accountant-001',
    '<EMAIL>',
    'accountant',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL8.vKzFu', -- Account123!
    'فاطمة',
    'المحاسبة',
    '+************',
    true,
    'ACCOUNTANT',
    true,
    'comp-demo-001',
    NOW(),
    NOW()
),
-- مندوب مبيعات
(
    'user-sales-001',
    '<EMAIL>',
    'sales',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL8.vKzFu', -- Sales123!
    'محمد',
    'المبيعات',
    '+************',
    true,
    'SALES_REP',
    true,
    'comp-demo-001',
    NOW(),
    NOW()
);

-- إدراج عملاء تجريبيين
INSERT INTO customers (
    id,
    code,
    "nameAr",
    "nameEn",
    email,
    phone,
    mobile,
    "taxNumber",
    "vatNumber",
    "addressAr",
    "addressEn",
    city,
    "postalCode",
    country,
    "creditLimit",
    "paymentTerms",
    "isActive",
    "customerType",
    "companyId",
    "createdAt",
    "updatedAt"
) VALUES 
(
    'cust-001',
    'CUST-001',
    'شركة الأعمال المتقدمة',
    'Advanced Business Company',
    '<EMAIL>',
    '+************',
    '+************',
    'TAX987654321',
    '***************',
    'طريق الملك عبدالعزيز، جدة',
    'King Abdulaziz Road, Jeddah',
    'جدة',
    '23456',
    'SA',
    50000.00,
    30,
    true,
    'COMPANY',
    'comp-demo-001',
    NOW(),
    NOW()
),
(
    'cust-002',
    'CUST-002',
    'محمد أحمد التجاري',
    'Mohammed Ahmed Trading',
    '<EMAIL>',
    '+966115678901',
    '+966505678901',
    NULL,
    NULL,
    'شارع التحلية، الخبر',
    'Tahlia Street, Khobar',
    'الخبر',
    '34567',
    'SA',
    25000.00,
    15,
    true,
    'INDIVIDUAL',
    'comp-demo-001',
    NOW(),
    NOW()
);

-- إدراج موردين تجريبيين
INSERT INTO suppliers (
    id,
    code,
    "nameAr",
    "nameEn",
    email,
    phone,
    mobile,
    "taxNumber",
    "vatNumber",
    "addressAr",
    "addressEn",
    city,
    "postalCode",
    country,
    "paymentTerms",
    "isActive",
    "supplierType",
    "companyId",
    "createdAt",
    "updatedAt"
) VALUES 
(
    'supp-001',
    'SUPP-001',
    'مؤسسة الإمدادات الصناعية',
    'Industrial Supplies Est.',
    '<EMAIL>',
    '+966116789012',
    '+966506789012',
    'TAX456789123',
    '345678912345678',
    'المنطقة الصناعية الثانية، الرياض',
    'Industrial Area 2, Riyadh',
    'الرياض',
    '45678',
    'SA',
    45,
    true,
    'LOCAL',
    'comp-demo-001',
    NOW(),
    NOW()
),
(
    'supp-002',
    'SUPP-002',
    'شركة التقنيات المتطورة',
    'Advanced Technologies Co.',
    '<EMAIL>',
    '+966117890123',
    '+966507890123',
    'TAX789123456',
    '3789*********01',
    'حي السليمانية، الرياض',
    'Sulaimaniya District, Riyadh',
    'الرياض',
    '56789',
    'SA',
    30,
    true,
    'LOCAL',
    'comp-demo-001',
    NOW(),
    NOW()
);

-- إدراج منتجات تجريبية
INSERT INTO products (
    id,
    code,
    "nameAr",
    "nameEn",
    description,
    barcode,
    category,
    unit,
    "costPrice",
    "sellingPrice",
    "vatRate",
    "minStock",
    "maxStock",
    "currentStock",
    "isActive",
    "isVatExempt",
    "companyId",
    "supplierId",
    "createdAt",
    "updatedAt"
) VALUES 
(
    'prod-001',
    'PROD-001',
    'جهاز كمبيوتر محمول',
    'Laptop Computer',
    'جهاز كمبيوتر محمول عالي الأداء للأعمال',
    '*********0123',
    'إلكترونيات',
    'قطعة',
    2500.00,
    3500.00,
    15.00,
    5,
    50,
    25,
    true,
    false,
    'comp-demo-001',
    'supp-002',
    NOW(),
    NOW()
),
(
    'prod-002',
    'PROD-002',
    'طابعة ليزر',
    'Laser Printer',
    'طابعة ليزر عالية الجودة للمكاتب',
    '**********234',
    'إلكترونيات',
    'قطعة',
    800.00,
    1200.00,
    15.00,
    3,
    30,
    15,
    true,
    false,
    'comp-demo-001',
    'supp-002',
    NOW(),
    NOW()
),
(
    'prod-003',
    'PROD-003',
    'ورق طباعة A4',
    'A4 Printing Paper',
    'ورق طباعة عالي الجودة مقاس A4',
    '**********345',
    'مكتبية',
    'علبة',
    25.00,
    35.00,
    15.00,
    20,
    200,
    100,
    true,
    false,
    'comp-demo-001',
    'supp-001',
    NOW(),
    NOW()
),
(
    'prod-004',
    'PROD-004',
    'أدوية أساسية',
    'Essential Medicines',
    'أدوية أساسية معفاة من ضريبة القيمة المضافة',
    '4567890123456',
    'طبية',
    'علبة',
    50.00,
    75.00,
    0.00,
    10,
    100,
    45,
    true,
    true,
    'comp-demo-001',
    'supp-001',
    NOW(),
    NOW()
);

-- إدراج موظفين تجريبيين
INSERT INTO employees (
    id,
    "employeeNumber",
    "firstNameAr",
    "lastNameAr",
    "firstNameEn",
    "lastNameEn",
    "nationalId",
    "iqamaNumber",
    email,
    phone,
    "birthDate",
    "hireDate",
    department,
    position,
    "basicSalary",
    allowances,
    "isActive",
    "bankAccount",
    iban,
    "companyId",
    "createdAt",
    "updatedAt"
) VALUES 
(
    'emp-001',
    'EMP-001',
    'عبدالله',
    'السعودي',
    'Abdullah',
    'Al-Saudi',
    '*********0',
    NULL,
    '<EMAIL>',
    '+************',
    '1990-01-15',
    '2023-01-01',
    'المبيعات',
    'مدير المبيعات',
    8000.00,
    1500.00,
    true,
    '*********0',
    'SA*********0*********012',
    'comp-demo-001',
    NOW(),
    NOW()
),
(
    'emp-002',
    'EMP-002',
    'نورا',
    'أحمد',
    'Nora',
    'Ahmed',
    '**********',
    NULL,
    '<EMAIL>',
    '+************',
    '1992-03-20',
    '2023-02-01',
    'المحاسبة',
    'محاسبة أولى',
    6500.00,
    1000.00,
    true,
    '**********',
    'SA234567890*********0123',
    'comp-demo-001',
    NOW(),
    NOW()
),
(
    'emp-003',
    'EMP-003',
    'أحمد',
    'محمد',
    'Ahmed',
    'Mohammed',
    NULL,
    '**********',
    '<EMAIL>',
    '+*********456',
    '1988-07-10',
    '2023-03-01',
    'المخازن',
    'أمين مخزن',
    4500.00,
    800.00,
    true,
    '**********',
    'SA34567890*********01234',
    'comp-demo-001',
    NOW(),
    NOW()
);

-- إدراج أصول ثابتة تجريبية
INSERT INTO "fixed_assets" (
    id,
    "assetNumber",
    "nameAr",
    "nameEn",
    category,
    "purchaseDate",
    "purchasePrice",
    "currentValue",
    "depreciationRate",
    "usefulLife",
    location,
    condition,
    "isActive",
    "companyId",
    "createdAt",
    "updatedAt"
) VALUES 
(
    'asset-001',
    'ASSET-001',
    'سيارة شركة',
    'Company Car',
    'مركبات',
    '2023-01-15',
    80000.00,
    75000.00,
    20.00,
    5,
    'المكتب الرئيسي',
    'GOOD',
    true,
    'comp-demo-001',
    NOW(),
    NOW()
),
(
    'asset-002',
    'ASSET-002',
    'أثاث مكتبي',
    'Office Furniture',
    'أثاث',
    '2023-02-01',
    25000.00,
    23000.00,
    10.00,
    10,
    'الطابق الأول',
    'EXCELLENT',
    true,
    'comp-demo-001',
    NOW(),
    NOW()
);

-- إدراج مبيعة تجريبية
INSERT INTO sales (
    id,
    "saleNumber",
    date,
    "customerId",
    subtotal,
    "vatAmount",
    total,
    discount,
    status,
    notes,
    "companyId",
    "createdById",
    "createdAt",
    "updatedAt"
) VALUES (
    'sale-001',
    'SALE-2024-001',
    '2024-01-15',
    'cust-001',
    4000.00,
    600.00,
    4600.00,
    0.00,
    'CONFIRMED',
    'مبيعة تجريبية للعميل الأول',
    'comp-demo-001',
    'user-sales-001',
    NOW(),
    NOW()
);

-- إدراج عناصر المبيعة
INSERT INTO "sale_items" (
    id,
    "saleId",
    "productId",
    quantity,
    "unitPrice",
    "vatRate",
    "vatAmount",
    total
) VALUES 
(
    'sale-item-001',
    'sale-001',
    'prod-001',
    1,
    3500.00,
    15.00,
    525.00,
    4025.00
),
(
    'sale-item-002',
    'sale-001',
    'prod-003',
    15,
    35.00,
    15.00,
    78.75,
    603.75
);

-- إدراج فاتورة تجريبية
INSERT INTO invoices (
    id,
    "invoiceNumber",
    date,
    "dueDate",
    "customerId",
    "saleId",
    subtotal,
    "vatAmount",
    total,
    discount,
    "paidAmount",
    status,
    "paymentStatus",
    "isEInvoice",
    notes,
    "companyId",
    "createdById",
    "createdAt",
    "updatedAt"
) VALUES (
    'inv-001',
    'INV-2024-001',
    '2024-01-15',
    '2024-02-14',
    'cust-001',
    'sale-001',
    4000.00,
    600.00,
    4600.00,
    0.00,
    2300.00,
    'SENT',
    'PARTIALLY_PAID',
    true,
    'فاتورة تجريبية - دفع جزئي',
    'comp-demo-001',
    'user-accountant-001',
    NOW(),
    NOW()
);

-- إدراج عناصر الفاتورة
INSERT INTO "invoice_items" (
    id,
    "invoiceId",
    "productId",
    description,
    quantity,
    "unitPrice",
    "vatRate",
    "vatAmount",
    total
) VALUES 
(
    'inv-item-001',
    'inv-001',
    'prod-001',
    'جهاز كمبيوتر محمول عالي الأداء',
    1,
    3500.00,
    15.00,
    525.00,
    4025.00
),
(
    'inv-item-002',
    'inv-001',
    'prod-003',
    'ورق طباعة A4 - 15 علبة',
    15,
    35.00,
    15.00,
    78.75,
    603.75
);

-- إدراج دفعة تجريبية
INSERT INTO payments (
    id,
    "invoiceId",
    amount,
    "paymentDate",
    "paymentMethod",
    reference,
    notes,
    "createdAt"
) VALUES (
    'pay-001',
    'inv-001',
    2300.00,
    '2024-01-20',
    'BANK_TRANSFER',
    'TXN-*********',
    'دفعة جزئية - تحويل بنكي',
    NOW()
);

-- إدراج حركات مخزون تجريبية
INSERT INTO "stock_movements" (
    id,
    "productId",
    "movementType",
    quantity,
    "unitCost",
    reference,
    notes,
    date
) VALUES 
(
    'stock-001',
    'prod-001',
    'IN',
    30,
    2500.00,
    'PO-2024-001',
    'استلام بضاعة من المورد',
    '2024-01-01'
),
(
    'stock-002',
    'prod-001',
    'OUT',
    5,
    2500.00,
    'SALE-2024-001',
    'بيع للعملاء',
    '2024-01-15'
),
(
    'stock-003',
    'prod-003',
    'IN',
    150,
    25.00,
    'PO-2024-002',
    'استلام ورق طباعة',
    '2024-01-05'
),
(
    'stock-004',
    'prod-003',
    'OUT',
    50,
    25.00,
    'SALE-2024-001',
    'بيع ورق طباعة',
    '2024-01-15'
);

-- تحديث المخزون الحالي للمنتجات
UPDATE products SET "currentStock" = 25 WHERE id = 'prod-001';
UPDATE products SET "currentStock" = 100 WHERE id = 'prod-003';

COMMIT;
