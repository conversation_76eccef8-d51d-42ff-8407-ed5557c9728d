import { Request } from 'express';
import { User, Company } from '@prisma/client';

// Extend Express Request interface
export interface AuthenticatedRequest extends Request {
  user?: User;
  company?: Company;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
  twoFactorCode?: string;
}

export interface RegisterData {
  email: string;
  username: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  companyId?: string;
}

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  companyId?: string;
  iat?: number;
  exp?: number;
}

// VAT types
export interface VATCalculation {
  subtotal: number;
  vatRate: number;
  vatAmount: number;
  total: number;
}

export interface VATReturnData {
  periodStart: Date;
  periodEnd: Date;
  totalSales: number;
  totalPurchases: number;
  outputVAT: number;
  inputVAT: number;
  netVAT: number;
}

// E-Invoice types
export interface EInvoiceData {
  invoiceNumber: string;
  issueDate: string;
  dueDate: string;
  seller: {
    vatNumber: string;
    name: string;
    address: string;
  };
  buyer: {
    vatNumber?: string;
    name: string;
    address: string;
  };
  items: EInvoiceItem[];
  totals: {
    subtotal: number;
    vatAmount: number;
    total: number;
  };
}

export interface EInvoiceItem {
  name: string;
  quantity: number;
  unitPrice: number;
  vatRate: number;
  vatAmount: number;
  total: number;
}

// ZATCA Integration types
export interface ZATCAResponse {
  success: boolean;
  uuid?: string;
  hash?: string;
  qrCode?: string;
  errors?: string[];
}

// Report types
export interface SalesReport {
  period: {
    start: Date;
    end: Date;
  };
  totalSales: number;
  totalVAT: number;
  salesCount: number;
  topProducts: Array<{
    productId: string;
    productName: string;
    quantity: number;
    revenue: number;
  }>;
  topCustomers: Array<{
    customerId: string;
    customerName: string;
    totalSales: number;
  }>;
}

export interface FinancialReport {
  period: {
    start: Date;
    end: Date;
  };
  revenue: number;
  expenses: number;
  profit: number;
  vatCollected: number;
  vatPaid: number;
  netVAT: number;
}

export interface InventoryReport {
  totalProducts: number;
  totalValue: number;
  lowStockItems: Array<{
    productId: string;
    productName: string;
    currentStock: number;
    minStock: number;
  }>;
  topMovingProducts: Array<{
    productId: string;
    productName: string;
    movementCount: number;
  }>;
}

// Audit types
export interface AuditLogData {
  userId: string;
  action: string;
  tableName: string;
  recordId: string;
  oldValues?: any;
  newValues?: any;
  ipAddress?: string;
  userAgent?: string;
}

// File upload types
export interface FileUploadResult {
  filename: string;
  originalName: string;
  size: number;
  mimetype: string;
  path: string;
  url: string;
}

// Notification types
export interface NotificationData {
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  userId?: string;
  companyId?: string;
  data?: any;
}

// Search and filter types
export interface SearchFilters {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: string;
  category?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Dashboard types
export interface DashboardStats {
  totalSales: number;
  totalPurchases: number;
  totalCustomers: number;
  totalSuppliers: number;
  totalProducts: number;
  totalInvoices: number;
  pendingPayments: number;
  lowStockItems: number;
  recentSales: any[];
  recentInvoices: any[];
  topProducts: any[];
  salesChart: Array<{
    date: string;
    sales: number;
    purchases: number;
  }>;
}

// Backup types
export interface BackupInfo {
  filename: string;
  size: number;
  createdAt: Date;
  type: 'manual' | 'automatic';
  status: 'completed' | 'failed' | 'in_progress';
}

// Settings types
export interface CompanySettings {
  vatRate: number;
  currency: string;
  language: string;
  timezone: string;
  dateFormat: string;
  numberFormat: string;
  fiscalYearStart: string;
  invoicePrefix: string;
  quotePrefix: string;
  receiptPrefix: string;
  autoBackup: boolean;
  backupFrequency: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
}

// Error types
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}
