// نظام ERP السعودي - خدمات API
// Saudi ERP System - API Services

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse, LoginForm, RegisterForm, User, DashboardStats } from '@/types';

// إعداد Axios
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // إضافة interceptor للطلبات
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // إضافة interceptor للاستجابات
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // إزالة التوكن المنتهي الصلاحية
          localStorage.removeItem('token');
          // إعادة توجيه لصفحة تسجيل الدخول
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // طرق HTTP الأساسية
  async get<T>(url: string, params?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.get(url, { params });
  }

  async post<T>(url: string, data?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.post(url, data);
  }

  async put<T>(url: string, data?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.put(url, data);
  }

  async delete<T>(url: string): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.delete(url);
  }

  // تعيين توكن المصادقة
  setAuthToken(token: string) {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  // إزالة توكن المصادقة
  removeAuthToken() {
    delete this.client.defaults.headers.common['Authorization'];
  }
}

// إنشاء instance من ApiClient
const apiClient = new ApiClient();

// ===== خدمات المصادقة =====
export const authAPI = {
  // تسجيل الدخول
  login: async (credentials: LoginForm): Promise<ApiResponse<{ user: User; token: string }>> => {
    const response = await apiClient.post('/auth/login', credentials);
    return response.data;
  },

  // التسجيل
  register: async (userData: RegisterForm): Promise<ApiResponse<{ user: User; token: string }>> => {
    const response = await apiClient.post('/auth/register', userData);
    return response.data;
  },

  // الحصول على الملف الشخصي
  getProfile: async (): Promise<ApiResponse<{ user: User }>> => {
    const response = await apiClient.get('/auth/me');
    return response.data;
  },

  // تحديث الملف الشخصي
  updateProfile: async (userData: Partial<User>): Promise<ApiResponse<{ user: User }>> => {
    const response = await apiClient.put('/auth/profile', userData);
    return response.data;
  },

  // تغيير كلمة المرور
  changePassword: async (passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<ApiResponse> => {
    const response = await apiClient.put('/auth/change-password', passwordData);
    return response.data;
  },

  // تسجيل الخروج
  logout: async (): Promise<ApiResponse> => {
    const response = await apiClient.post('/auth/logout');
    return response.data;
  },

  // تحديث التوكن
  refreshToken: async (): Promise<ApiResponse<{ token: string }>> => {
    const response = await apiClient.post('/auth/refresh');
    return response.data;
  },

  // تعيين توكن المصادقة
  setAuthToken: (token: string) => {
    apiClient.setAuthToken(token);
  },

  // إزالة توكن المصادقة
  removeAuthToken: () => {
    apiClient.removeAuthToken();
  },
};

// ===== خدمات الشركة =====
export const companyAPI = {
  // الحصول على معلومات الشركة
  getCompany: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/company');
    return response.data;
  },

  // تحديث معلومات الشركة
  updateCompany: async (companyData: any): Promise<ApiResponse> => {
    const response = await apiClient.put('/company', companyData);
    return response.data;
  },

  // الحصول على إحصائيات الشركة
  getStats: async (): Promise<ApiResponse<{ stats: DashboardStats }>> => {
    const response = await apiClient.get('/company/stats');
    return response.data;
  },

  // الحصول على إعدادات الشركة
  getSettings: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/company/settings');
    return response.data;
  },
};

// ===== خدمات العملاء =====
export const customerAPI = {
  // الحصول على قائمة العملاء
  getCustomers: async (params?: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/customers', params);
    return response.data;
  },

  // الحصول على عميل محدد
  getCustomer: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.get(`/customers/${id}`);
    return response.data;
  },

  // إنشاء عميل جديد
  createCustomer: async (customerData: any): Promise<ApiResponse> => {
    const response = await apiClient.post('/customers', customerData);
    return response.data;
  },

  // تحديث عميل
  updateCustomer: async (id: string, customerData: any): Promise<ApiResponse> => {
    const response = await apiClient.put(`/customers/${id}`, customerData);
    return response.data;
  },

  // حذف عميل
  deleteCustomer: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/customers/${id}`);
    return response.data;
  },
};

// ===== خدمات الموردين =====
export const supplierAPI = {
  // الحصول على قائمة الموردين
  getSuppliers: async (params?: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/suppliers', params);
    return response.data;
  },

  // الحصول على مورد محدد
  getSupplier: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.get(`/suppliers/${id}`);
    return response.data;
  },

  // إنشاء مورد جديد
  createSupplier: async (supplierData: any): Promise<ApiResponse> => {
    const response = await apiClient.post('/suppliers', supplierData);
    return response.data;
  },

  // تحديث مورد
  updateSupplier: async (id: string, supplierData: any): Promise<ApiResponse> => {
    const response = await apiClient.put(`/suppliers/${id}`, supplierData);
    return response.data;
  },

  // حذف مورد
  deleteSupplier: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/suppliers/${id}`);
    return response.data;
  },
};

// ===== خدمات المنتجات =====
export const productAPI = {
  // الحصول على قائمة المنتجات
  getProducts: async (params?: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/products', params);
    return response.data;
  },

  // الحصول على منتج محدد
  getProduct: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.get(`/products/${id}`);
    return response.data;
  },

  // إنشاء منتج جديد
  createProduct: async (productData: any): Promise<ApiResponse> => {
    const response = await apiClient.post('/products', productData);
    return response.data;
  },

  // تحديث منتج
  updateProduct: async (id: string, productData: any): Promise<ApiResponse> => {
    const response = await apiClient.put(`/products/${id}`, productData);
    return response.data;
  },

  // حذف منتج
  deleteProduct: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/products/${id}`);
    return response.data;
  },

  // البحث عن المنتجات
  searchProducts: async (query: string): Promise<ApiResponse> => {
    const response = await apiClient.get('/products/search', { q: query });
    return response.data;
  },
};

// ===== خدمات المبيعات =====
export const salesAPI = {
  // الحصول على قائمة المبيعات
  getSales: async (params?: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/sales', params);
    return response.data;
  },

  // الحصول على مبيعة محددة
  getSale: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.get(`/sales/${id}`);
    return response.data;
  },

  // إنشاء مبيعة جديدة
  createSale: async (saleData: any): Promise<ApiResponse> => {
    const response = await apiClient.post('/sales', saleData);
    return response.data;
  },

  // تحديث مبيعة
  updateSale: async (id: string, saleData: any): Promise<ApiResponse> => {
    const response = await apiClient.put(`/sales/${id}`, saleData);
    return response.data;
  },

  // حذف مبيعة
  deleteSale: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/sales/${id}`);
    return response.data;
  },
};

// ===== خدمات الفواتير =====
export const invoiceAPI = {
  // الحصول على قائمة الفواتير
  getInvoices: async (params?: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/invoices', params);
    return response.data;
  },

  // الحصول على فاتورة محددة
  getInvoice: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.get(`/invoices/${id}`);
    return response.data;
  },

  // إنشاء فاتورة جديدة
  createInvoice: async (invoiceData: any): Promise<ApiResponse> => {
    const response = await apiClient.post('/invoices', invoiceData);
    return response.data;
  },

  // تحديث فاتورة
  updateInvoice: async (id: string, invoiceData: any): Promise<ApiResponse> => {
    const response = await apiClient.put(`/invoices/${id}`, invoiceData);
    return response.data;
  },

  // حذف فاتورة
  deleteInvoice: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/invoices/${id}`);
    return response.data;
  },

  // إرسال فاتورة إلكترونية
  sendEInvoice: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.post(`/invoices/${id}/send-einvoice`);
    return response.data;
  },

  // طباعة فاتورة
  printInvoice: async (id: string): Promise<Blob> => {
    const response = await apiClient.get(`/invoices/${id}/print`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

// ===== خدمات ضريبة القيمة المضافة =====
export const vatAPI = {
  // الحصول على إقرارات ضريبة القيمة المضافة
  getVATReturns: async (params?: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/vat/returns', params);
    return response.data;
  },

  // إنشاء إقرار ضريبة القيمة المضافة
  createVATReturn: async (vatData: any): Promise<ApiResponse> => {
    const response = await apiClient.post('/vat/returns', vatData);
    return response.data;
  },

  // تقديم إقرار ضريبة القيمة المضافة
  submitVATReturn: async (id: string): Promise<ApiResponse> => {
    const response = await apiClient.post(`/vat/returns/${id}/submit`);
    return response.data;
  },

  // الحصول على ملخص ضريبة القيمة المضافة
  getVATSummary: async (params: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/vat/summary', params);
    return response.data;
  },
};

// ===== خدمات لوحة التحكم =====
export const dashboardAPI = {
  // الحصول على إحصائيات لوحة التحكم
  getStats: async (): Promise<ApiResponse<{ stats: DashboardStats }>> => {
    const response = await apiClient.get('/company/stats');
    return response.data;
  },

  // الحصول على بيانات الرسم البياني للمبيعات
  getSalesChart: async (params: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/reports/sales-chart', params);
    return response.data;
  },

  // الحصول على المعاملات الأخيرة
  getRecentTransactions: async (params: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/reports/recent-transactions', params);
    return response.data;
  },

  // الحصول على أفضل المنتجات
  getTopProducts: async (params: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/reports/top-products', params);
    return response.data;
  },
};

// ===== خدمات التقارير =====
export const reportAPI = {
  // تقرير المبيعات
  getSalesReport: async (params: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/reports/sales', params);
    return response.data;
  },

  // تقرير المشتريات
  getPurchasesReport: async (params: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/reports/purchases', params);
    return response.data;
  },

  // تقرير المخزون
  getInventoryReport: async (params: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/reports/inventory', params);
    return response.data;
  },

  // تقرير مالي
  getFinancialReport: async (params: any): Promise<ApiResponse> => {
    const response = await apiClient.get('/reports/financial', params);
    return response.data;
  },

  // تصدير تقرير
  exportReport: async (type: string, params: any): Promise<Blob> => {
    const response = await apiClient.get(`/reports/${type}/export`, {
      params,
      responseType: 'blob',
    });
    return response.data;
  },
};

// تصدير العميل الرئيسي
export default apiClient;
