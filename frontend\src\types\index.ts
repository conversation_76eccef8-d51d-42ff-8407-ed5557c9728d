// نظام ERP السعودي - أنواع TypeScript
// Saudi ERP System - TypeScript Types

// ===== User Types =====
export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: UserRole;
  isActive: boolean;
  emailVerified: boolean;
  twoFactorEnabled: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  company?: Company;
}

export type UserRole = 
  | 'SUPER_ADMIN'
  | 'ADMIN'
  | 'MANAGER'
  | 'ACCOUNTANT'
  | 'SALES_REP'
  | 'WAREHOUSE_KEEPER'
  | 'USER';

// ===== Company Types =====
export interface Company {
  id: string;
  nameAr: string;
  nameEn: string;
  commercialRegister: string;
  taxNumber: string;
  vatNumber?: string;
  phone: string;
  email: string;
  website?: string;
  addressAr: string;
  addressEn: string;
  city: string;
  postalCode: string;
  country: string;
  isActive: boolean;
  subscriptionPlan: SubscriptionPlan;
  subscriptionExpiry?: string;
  createdAt: string;
  updatedAt: string;
}

export type SubscriptionPlan = 'BASIC' | 'PROFESSIONAL' | 'ENTERPRISE';

// ===== Customer Types =====
export interface Customer {
  id: string;
  code: string;
  nameAr: string;
  nameEn?: string;
  email?: string;
  phone: string;
  mobile?: string;
  taxNumber?: string;
  vatNumber?: string;
  addressAr: string;
  addressEn?: string;
  city: string;
  postalCode?: string;
  country: string;
  creditLimit: number;
  paymentTerms: number;
  isActive: boolean;
  customerType: CustomerType;
  createdAt: string;
  updatedAt: string;
}

export type CustomerType = 'INDIVIDUAL' | 'COMPANY' | 'GOVERNMENT';

// ===== Supplier Types =====
export interface Supplier {
  id: string;
  code: string;
  nameAr: string;
  nameEn?: string;
  email?: string;
  phone: string;
  mobile?: string;
  taxNumber?: string;
  vatNumber?: string;
  addressAr: string;
  addressEn?: string;
  city: string;
  postalCode?: string;
  country: string;
  paymentTerms: number;
  isActive: boolean;
  supplierType: SupplierType;
  createdAt: string;
  updatedAt: string;
}

export type SupplierType = 'LOCAL' | 'INTERNATIONAL';

// ===== Product Types =====
export interface Product {
  id: string;
  code: string;
  nameAr: string;
  nameEn?: string;
  description?: string;
  barcode?: string;
  category: string;
  unit: string;
  costPrice: number;
  sellingPrice: number;
  vatRate: number;
  minStock: number;
  maxStock?: number;
  currentStock: number;
  isActive: boolean;
  isVatExempt: boolean;
  createdAt: string;
  updatedAt: string;
  supplier?: Supplier;
}

// ===== Sales Types =====
export interface Sale {
  id: string;
  saleNumber: string;
  date: string;
  customer: Customer;
  subtotal: number;
  vatAmount: number;
  total: number;
  discount: number;
  status: SaleStatus;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  items: SaleItem[];
}

export interface SaleItem {
  id: string;
  product: Product;
  quantity: number;
  unitPrice: number;
  vatRate: number;
  vatAmount: number;
  total: number;
}

export type SaleStatus = 'DRAFT' | 'CONFIRMED' | 'INVOICED' | 'CANCELLED';

// ===== Purchase Types =====
export interface Purchase {
  id: string;
  purchaseNumber: string;
  date: string;
  supplier: Supplier;
  subtotal: number;
  vatAmount: number;
  total: number;
  discount: number;
  status: PurchaseStatus;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  items: PurchaseItem[];
}

export interface PurchaseItem {
  id: string;
  product: Product;
  quantity: number;
  unitPrice: number;
  vatRate: number;
  vatAmount: number;
  total: number;
}

export type PurchaseStatus = 'DRAFT' | 'CONFIRMED' | 'RECEIVED' | 'CANCELLED';

// ===== Invoice Types =====
export interface Invoice {
  id: string;
  invoiceNumber: string;
  date: string;
  dueDate: string;
  customer: Customer;
  sale?: Sale;
  subtotal: number;
  vatAmount: number;
  total: number;
  discount: number;
  paidAmount: number;
  status: InvoiceStatus;
  paymentStatus: PaymentStatus;
  isEInvoice: boolean;
  eInvoiceUUID?: string;
  qrCode?: string;
  zatcaHash?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  items: InvoiceItem[];
  payments: Payment[];
}

export interface InvoiceItem {
  id: string;
  product: Product;
  description: string;
  quantity: number;
  unitPrice: number;
  vatRate: number;
  vatAmount: number;
  total: number;
}

export type InvoiceStatus = 'DRAFT' | 'SENT' | 'APPROVED' | 'CANCELLED';
export type PaymentStatus = 'UNPAID' | 'PARTIALLY_PAID' | 'PAID' | 'OVERDUE';

// ===== Payment Types =====
export interface Payment {
  id: string;
  invoice: Invoice;
  amount: number;
  paymentDate: string;
  paymentMethod: PaymentMethod;
  reference?: string;
  notes?: string;
  createdAt: string;
}

export type PaymentMethod = 'CASH' | 'BANK_TRANSFER' | 'CREDIT_CARD' | 'CHEQUE' | 'OTHER';

// ===== Employee Types =====
export interface Employee {
  id: string;
  employeeNumber: string;
  firstNameAr: string;
  lastNameAr: string;
  firstNameEn?: string;
  lastNameEn?: string;
  nationalId: string;
  iqamaNumber?: string;
  email?: string;
  phone: string;
  birthDate: string;
  hireDate: string;
  department: string;
  position: string;
  basicSalary: number;
  allowances: number;
  isActive: boolean;
  bankAccount?: string;
  iban?: string;
  createdAt: string;
  updatedAt: string;
}

// ===== Payroll Types =====
export interface Payroll {
  id: string;
  employee: Employee;
  month: number;
  year: number;
  basicSalary: number;
  allowances: number;
  overtime: number;
  deductions: number;
  gosi: number;
  netSalary: number;
  status: PayrollStatus;
  paidAt?: string;
  createdAt: string;
  updatedAt: string;
}

export type PayrollStatus = 'DRAFT' | 'APPROVED' | 'PAID';

// ===== Fixed Asset Types =====
export interface FixedAsset {
  id: string;
  assetNumber: string;
  nameAr: string;
  nameEn?: string;
  category: string;
  purchaseDate: string;
  purchasePrice: number;
  currentValue: number;
  depreciationRate: number;
  usefulLife: number;
  location?: string;
  condition: AssetCondition;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export type AssetCondition = 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'DAMAGED';

// ===== VAT Types =====
export interface VATReturn {
  id: string;
  periodStart: string;
  periodEnd: string;
  totalSales: number;
  totalPurchases: number;
  outputVAT: number;
  inputVAT: number;
  netVAT: number;
  status: VATReturnStatus;
  submittedAt?: string;
  zatcaReference?: string;
  createdAt: string;
  updatedAt: string;
}

export type VATReturnStatus = 'DRAFT' | 'SUBMITTED' | 'APPROVED' | 'REJECTED';

// ===== API Response Types =====
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// ===== Form Types =====
export interface LoginForm {
  email: string;
  password: string;
  twoFactorCode?: string;
}

export interface RegisterForm {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone?: string;
  companyId?: string;
}

export interface CustomerForm {
  nameAr: string;
  nameEn?: string;
  email?: string;
  phone: string;
  mobile?: string;
  taxNumber?: string;
  vatNumber?: string;
  addressAr: string;
  addressEn?: string;
  city: string;
  postalCode?: string;
  country: string;
  creditLimit: number;
  paymentTerms: number;
  customerType: CustomerType;
}

export interface ProductForm {
  nameAr: string;
  nameEn?: string;
  description?: string;
  barcode?: string;
  category: string;
  unit: string;
  costPrice: number;
  sellingPrice: number;
  vatRate: number;
  minStock: number;
  maxStock?: number;
  currentStock: number;
  isVatExempt: boolean;
  supplierId?: string;
}

export interface SaleForm {
  customerId: string;
  date: string;
  discount: number;
  notes?: string;
  items: SaleItemForm[];
}

export interface SaleItemForm {
  productId: string;
  quantity: number;
  unitPrice: number;
}

// ===== Dashboard Types =====
export interface DashboardStats {
  overview: {
    totalCustomers: number;
    totalSuppliers: number;
    totalProducts: number;
    totalEmployees: number;
  };
  monthly: {
    sales: {
      amount: number;
      count: number;
    };
    purchases: {
      amount: number;
      count: number;
    };
  };
  alerts: {
    pendingInvoices: {
      amount: number;
      count: number;
    };
    lowStockProducts: number;
  };
}

export interface ChartData {
  date: string;
  sales: number;
  purchases: number;
}

// ===== Filter Types =====
export interface SearchFilters {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: string;
  category?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// ===== Theme Types =====
export interface ThemeContextType {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
}

// ===== Notification Types =====
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// ===== File Upload Types =====
export interface FileUpload {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  url?: string;
  error?: string;
}

// ===== Report Types =====
export interface ReportParams {
  type: string;
  dateFrom: string;
  dateTo: string;
  format: 'pdf' | 'excel' | 'csv';
  filters?: Record<string, any>;
}

// ===== Settings Types =====
export interface CompanySettings {
  vatRate: number;
  currency: string;
  language: string;
  timezone: string;
  dateFormat: string;
  numberFormat: string;
  fiscalYearStart: string;
  invoicePrefix: string;
  quotePrefix: string;
  receiptPrefix: string;
  autoBackup: boolean;
  backupFrequency: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
}
