{"name": "saudi-erp-system", "version": "1.0.0", "description": "نظام ERP متكامل للمؤسسات والشركات السعودية مع التوافق الكامل مع متطلبات هيئة الزكاة والدخل", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["ERP", "Saudi Arabia", "VAT", "ZATCA", "E-Invoice", "Accounting", "Arabic"], "author": "Saudi ERP Development Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "moment": "^2.29.4", "moment-hijri": "^2.1.2", "uuid": "^9.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "qrcode": "^1.5.3", "pdf-lib": "^1.17.1", "xlsx": "^0.18.5", "compression": "^1.7.4", "rate-limiter-flexible": "^4.0.1", "winston": "^3.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/qrcode": "^1.5.5", "@types/compression": "^1.7.5", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "prettier": "^3.1.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.2", "prisma": "^5.7.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}