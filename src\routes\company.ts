import { Router } from 'express';
import { prisma } from '../server';
import { validateRequest } from '@/utils/validation';
import { companySchemas } from '@/utils/validation';
import { asyncHandler } from '@/middleware/errorHandler';
import { AuthenticatedRequest } from '@/types';
import { requireRole } from '@/middleware/auth';

const router = Router();

/**
 * @route   GET /api/company
 * @desc    Get company information
 * @access  Private
 */
router.get('/',
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const user = req.user!;
    
    let company;
    if (user.companyId) {
      company = await prisma.company.findUnique({
        where: { id: user.companyId },
        include: {
          users: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              role: true,
              isActive: true,
            }
          },
          _count: {
            select: {
              customers: true,
              suppliers: true,
              products: true,
              sales: true,
              purchases: true,
              invoices: true,
              employees: true,
            }
          }
        }
      });
    }

    res.json({
      success: true,
      message: 'Company information retrieved successfully',
      data: { company },
    });
  })
);

/**
 * @route   POST /api/company
 * @desc    Create a new company
 * @access  Private (Admin only)
 */
router.post('/',
  requireRole(['SUPER_ADMIN', 'ADMIN']),
  validateRequest(companySchemas.create),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const companyData = req.body;

    // Check if commercial register already exists
    const existingCompany = await prisma.company.findFirst({
      where: {
        OR: [
          { commercialRegister: companyData.commercialRegister },
          { taxNumber: companyData.taxNumber },
          { vatNumber: companyData.vatNumber },
        ]
      }
    });

    if (existingCompany) {
      return res.status(400).json({
        success: false,
        message: 'Company with this commercial register, tax number, or VAT number already exists',
      });
    }

    // Generate company code
    const companyCode = `COMP-${Date.now()}`;

    const company = await prisma.company.create({
      data: {
        ...companyData,
        code: companyCode,
      },
      include: {
        _count: {
          select: {
            users: true,
            customers: true,
            suppliers: true,
            products: true,
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Company created successfully',
      data: { company },
    });
  })
);

/**
 * @route   PUT /api/company/:id
 * @desc    Update company information
 * @access  Private (Admin only)
 */
router.put('/:id',
  requireRole(['SUPER_ADMIN', 'ADMIN']),
  validateRequest(companySchemas.update),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const user = req.user!;

    // Check if company exists and user has access
    const existingCompany = await prisma.company.findUnique({
      where: { id }
    });

    if (!existingCompany) {
      return res.status(404).json({
        success: false,
        message: 'Company not found',
      });
    }

    // Super admin can update any company, others can only update their own
    if (user.role !== 'SUPER_ADMIN' && user.companyId !== id) {
      return res.status(403).json({
        success: false,
        message: 'You can only update your own company',
      });
    }

    const updatedCompany = await prisma.company.update({
      where: { id },
      data: updateData,
      include: {
        _count: {
          select: {
            users: true,
            customers: true,
            suppliers: true,
            products: true,
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Company updated successfully',
      data: { company: updatedCompany },
    });
  })
);

/**
 * @route   GET /api/company/stats
 * @desc    Get company statistics
 * @access  Private
 */
router.get('/stats',
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const user = req.user!;
    
    if (!user.companyId) {
      return res.status(400).json({
        success: false,
        message: 'User is not associated with any company',
      });
    }

    // Get current month and year
    const now = new Date();
    const currentMonth = now.getMonth() + 1;
    const currentYear = now.getFullYear();
    const firstDayOfMonth = new Date(currentYear, now.getMonth(), 1);
    const lastDayOfMonth = new Date(currentYear, now.getMonth() + 1, 0);

    // Get statistics
    const [
      totalCustomers,
      totalSuppliers,
      totalProducts,
      totalEmployees,
      monthlySales,
      monthlyPurchases,
      pendingInvoices,
      lowStockProducts,
    ] = await Promise.all([
      // Total customers
      prisma.customer.count({
        where: { companyId: user.companyId, isActive: true }
      }),
      
      // Total suppliers
      prisma.supplier.count({
        where: { companyId: user.companyId, isActive: true }
      }),
      
      // Total products
      prisma.product.count({
        where: { companyId: user.companyId, isActive: true }
      }),
      
      // Total employees
      prisma.employee.count({
        where: { companyId: user.companyId, isActive: true }
      }),
      
      // Monthly sales
      prisma.sale.aggregate({
        where: {
          companyId: user.companyId,
          date: {
            gte: firstDayOfMonth,
            lte: lastDayOfMonth,
          },
          status: { not: 'CANCELLED' }
        },
        _sum: { total: true },
        _count: true,
      }),
      
      // Monthly purchases
      prisma.purchase.aggregate({
        where: {
          companyId: user.companyId,
          date: {
            gte: firstDayOfMonth,
            lte: lastDayOfMonth,
          },
          status: { not: 'CANCELLED' }
        },
        _sum: { total: true },
        _count: true,
      }),
      
      // Pending invoices
      prisma.invoice.aggregate({
        where: {
          companyId: user.companyId,
          paymentStatus: { in: ['UNPAID', 'PARTIALLY_PAID'] }
        },
        _sum: { total: true },
        _count: true,
      }),
      
      // Low stock products
      prisma.product.count({
        where: {
          companyId: user.companyId,
          isActive: true,
          currentStock: { lte: prisma.product.fields.minStock }
        }
      }),
    ]);

    const stats = {
      overview: {
        totalCustomers,
        totalSuppliers,
        totalProducts,
        totalEmployees,
      },
      monthly: {
        sales: {
          amount: monthlySales._sum.total || 0,
          count: monthlySales._count,
        },
        purchases: {
          amount: monthlyPurchases._sum.total || 0,
          count: monthlyPurchases._count,
        },
      },
      alerts: {
        pendingInvoices: {
          amount: pendingInvoices._sum.total || 0,
          count: pendingInvoices._count,
        },
        lowStockProducts,
      },
    };

    res.json({
      success: true,
      message: 'Company statistics retrieved successfully',
      data: { stats },
    });
  })
);

/**
 * @route   GET /api/company/settings
 * @desc    Get company settings
 * @access  Private (Admin only)
 */
router.get('/settings',
  requireRole(['SUPER_ADMIN', 'ADMIN', 'MANAGER']),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const user = req.user!;
    
    if (!user.companyId) {
      return res.status(400).json({
        success: false,
        message: 'User is not associated with any company',
      });
    }

    const company = await prisma.company.findUnique({
      where: { id: user.companyId },
      select: {
        id: true,
        nameAr: true,
        nameEn: true,
        commercialRegister: true,
        taxNumber: true,
        vatNumber: true,
        phone: true,
        email: true,
        website: true,
        addressAr: true,
        addressEn: true,
        city: true,
        postalCode: true,
        country: true,
        subscriptionPlan: true,
        subscriptionExpiry: true,
        isActive: true,
      }
    });

    res.json({
      success: true,
      message: 'Company settings retrieved successfully',
      data: { settings: company },
    });
  })
);

export default router;
