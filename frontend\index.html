<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/saudi-flag.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- SEO Meta Tags -->
    <title>نظام ERP السعودي المتكامل | Saudi Integrated ERP System</title>
    <meta name="description" content="نظام إدارة موارد المؤسسات متكامل مصمم خصيصاً للشركات السعودية مع التوافق الكامل مع متطلبات هيئة الزكاة والضرائب والجمارك" />
    <meta name="keywords" content="ERP, Saudi Arabia, VAT, ZATCA, E-Invoice, Accounting, المحاسبة, ضريبة القيمة المضافة, الفوترة الإلكترونية" />
    <meta name="author" content="Saudi ERP Development Team" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="نظام ERP السعودي المتكامل" />
    <meta property="og:description" content="نظام إدارة موارد المؤسسات متكامل للشركات السعودية" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://saudierp.com" />
    <meta property="og:image" content="/og-image.png" />
    <meta property="og:locale" content="ar_SA" />
    <meta property="og:locale:alternate" content="en_US" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="نظام ERP السعودي المتكامل" />
    <meta name="twitter:description" content="نظام إدارة موارد المؤسسات متكامل للشركات السعودية" />
    <meta name="twitter:image" content="/twitter-image.png" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#006c35">
    <meta name="msapplication-TileColor" content="#006c35">
    <meta name="theme-color" content="#006c35">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- PWA Meta Tags -->
    <meta name="application-name" content="Saudi ERP">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Saudi ERP">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="msapplication-config" content="/browserconfig.xml">
    <meta name="msapplication-tap-highlight" content="no">
    
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="/fonts/NotoSansArabic-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/fonts/Inter-Regular.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- Critical CSS -->
    <style>
      /* Critical CSS for faster loading */
      body {
        margin: 0;
        font-family: 'Noto Sans Arabic', 'Inter', system-ui, -apple-system, sans-serif;
        background-color: #f8fafc;
        color: #1e293b;
        direction: rtl;
      }
      
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #006c35 0%, #0ea5e9 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 2rem;
        animation: pulse 2s infinite;
      }
      
      .loading-text {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-align: center;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading screen when app loads */
      .app-loaded .loading-screen {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
      <div class="loading-logo">
        <svg viewBox="0 0 100 100" fill="currentColor">
          <!-- Saudi Flag Icon -->
          <rect width="100" height="60" fill="currentColor" rx="4"/>
          <text x="50" y="35" text-anchor="middle" font-size="12" fill="white">🇸🇦</text>
          <text x="50" y="50" text-anchor="middle" font-size="8" fill="white">ERP</text>
        </svg>
      </div>
      <div class="loading-text">
        نظام ERP السعودي المتكامل<br>
        <small style="font-size: 1rem; font-weight: 400;">Saudi Integrated ERP System</small>
      </div>
      <div class="loading-spinner"></div>
    </div>
    
    <!-- App Root -->
    <div id="root"></div>
    
    <!-- Service Worker Registration -->
    <script>
      // Register service worker for PWA
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
      
      // Hide loading screen when app loads
      window.addEventListener('load', () => {
        setTimeout(() => {
          document.body.classList.add('app-loaded');
        }, 1000);
      });
      
      // Language detection and direction setting
      const userLang = navigator.language || navigator.userLanguage;
      const isArabic = userLang.startsWith('ar');
      
      if (!isArabic) {
        document.documentElement.lang = 'en';
        document.documentElement.dir = 'ltr';
      }
      
      // Theme detection
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (prefersDark) {
        document.documentElement.setAttribute('data-theme', 'dark');
      }
    </script>
    
    <!-- Main App Script -->
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Analytics (Replace with your analytics code) -->
    <!-- 
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'GA_MEASUREMENT_ID');
    </script>
    -->
  </body>
</html>
