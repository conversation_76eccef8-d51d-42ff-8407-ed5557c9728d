{"name": "saudi-erp-frontend", "version": "1.0.0", "description": "واجهة نظام ERP السعودي - React Frontend", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write src/**/*.{ts,tsx,css,md}"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-select": "^5.8.0", "react-datepicker": "^4.24.0", "react-table": "^7.8.0", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1", "react-modal": "^3.16.1", "react-dropzone": "^14.2.3", "qrcode.react": "^3.1.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "moment": "^2.29.4", "moment-hijri": "^2.1.2", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-table": "^7.7.18", "@types/react-modal": "^3.16.3", "@types/react-datepicker": "^4.19.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}