import { VATCalculation } from '@/types';

/**
 * Calculate VAT for Saudi Arabia (15% standard rate)
 */
export const calculateVAT = (
  subtotal: number,
  vatRate: number = 15,
  isVatExempt: boolean = false
): VATCalculation => {
  if (isVatExempt || vatRate === 0) {
    return {
      subtotal,
      vatRate: 0,
      vatAmount: 0,
      total: subtotal,
    };
  }

  const vatAmount = Math.round((subtotal * vatRate / 100) * 100) / 100;
  const total = Math.round((subtotal + vatAmount) * 100) / 100;

  return {
    subtotal: Math.round(subtotal * 100) / 100,
    vatRate,
    vatAmount,
    total,
  };
};

/**
 * Calculate VAT from total amount (reverse calculation)
 */
export const calculateVATFromTotal = (
  total: number,
  vatRate: number = 15
): VATCalculation => {
  const subtotal = Math.round((total / (1 + vatRate / 100)) * 100) / 100;
  const vatAmount = Math.round((total - subtotal) * 100) / 100;

  return {
    subtotal,
    vatRate,
    vatAmount,
    total: Math.round(total * 100) / 100,
  };
};

/**
 * Calculate line item VAT
 */
export const calculateLineItemVAT = (
  quantity: number,
  unitPrice: number,
  vatRate: number = 15,
  isVatExempt: boolean = false
): VATCalculation => {
  const subtotal = quantity * unitPrice;
  return calculateVAT(subtotal, vatRate, isVatExempt);
};

/**
 * Calculate total VAT for multiple items
 */
export const calculateTotalVAT = (
  items: Array<{
    quantity: number;
    unitPrice: number;
    vatRate?: number;
    isVatExempt?: boolean;
  }>
): VATCalculation => {
  let totalSubtotal = 0;
  let totalVATAmount = 0;

  items.forEach(item => {
    const itemVAT = calculateLineItemVAT(
      item.quantity,
      item.unitPrice,
      item.vatRate || 15,
      item.isVatExempt || false
    );
    
    totalSubtotal += itemVAT.subtotal;
    totalVATAmount += itemVAT.vatAmount;
  });

  const total = totalSubtotal + totalVATAmount;

  return {
    subtotal: Math.round(totalSubtotal * 100) / 100,
    vatRate: totalSubtotal > 0 ? Math.round((totalVATAmount / totalSubtotal * 100) * 100) / 100 : 0,
    vatAmount: Math.round(totalVATAmount * 100) / 100,
    total: Math.round(total * 100) / 100,
  };
};

/**
 * Check if VAT number is valid (Saudi format)
 */
export const isValidVATNumber = (vatNumber: string): boolean => {
  // Saudi VAT number format: 3XXXXXXXXXX3 (15 digits starting and ending with 3)
  const vatRegex = /^3[0-9]{13}3$/;
  return vatRegex.test(vatNumber);
};

/**
 * Format VAT number for display
 */
export const formatVATNumber = (vatNumber: string): string => {
  if (!vatNumber) return '';
  
  // Remove any non-digit characters
  const cleaned = vatNumber.replace(/\D/g, '');
  
  // Format as XXX-XXX-XXX-XXX-X
  if (cleaned.length === 15) {
    return cleaned.replace(/(\d{3})(\d{3})(\d{3})(\d{3})(\d{3})/, '$1-$2-$3-$4-$5');
  }
  
  return vatNumber;
};

/**
 * Get VAT rate based on product category (Saudi specific)
 */
export const getVATRateByCategory = (category: string): number => {
  const exemptCategories = [
    'MEDICAL',
    'EDUCATION',
    'FOOD_BASIC',
    'REAL_ESTATE_RESIDENTIAL',
  ];

  const zeroRatedCategories = [
    'EXPORTS',
    'INTERNATIONAL_TRANSPORT',
    'PRECIOUS_METALS',
  ];

  const reducedRateCategories = [
    // Currently no reduced rate categories in Saudi Arabia
  ];

  if (exemptCategories.includes(category.toUpperCase())) {
    return 0; // Exempt
  }

  if (zeroRatedCategories.includes(category.toUpperCase())) {
    return 0; // Zero-rated
  }

  if (reducedRateCategories.includes(category.toUpperCase())) {
    return 5; // Reduced rate (if applicable in future)
  }

  return 15; // Standard rate
};

/**
 * Generate VAT summary for reporting
 */
export const generateVATSummary = (
  transactions: Array<{
    subtotal: number;
    vatAmount: number;
    total: number;
    type: 'SALE' | 'PURCHASE';
  }>
) => {
  const sales = transactions.filter(t => t.type === 'SALE');
  const purchases = transactions.filter(t => t.type === 'PURCHASE');

  const salesSubtotal = sales.reduce((sum, s) => sum + s.subtotal, 0);
  const salesVAT = sales.reduce((sum, s) => sum + s.vatAmount, 0);
  const salesTotal = sales.reduce((sum, s) => sum + s.total, 0);

  const purchasesSubtotal = purchases.reduce((sum, p) => sum + p.subtotal, 0);
  const purchasesVAT = purchases.reduce((sum, p) => sum + p.vatAmount, 0);
  const purchasesTotal = purchases.reduce((sum, p) => sum + p.total, 0);

  const netVAT = salesVAT - purchasesVAT;

  return {
    sales: {
      subtotal: Math.round(salesSubtotal * 100) / 100,
      vatAmount: Math.round(salesVAT * 100) / 100,
      total: Math.round(salesTotal * 100) / 100,
      count: sales.length,
    },
    purchases: {
      subtotal: Math.round(purchasesSubtotal * 100) / 100,
      vatAmount: Math.round(purchasesVAT * 100) / 100,
      total: Math.round(purchasesTotal * 100) / 100,
      count: purchases.length,
    },
    summary: {
      outputVAT: Math.round(salesVAT * 100) / 100,
      inputVAT: Math.round(purchasesVAT * 100) / 100,
      netVAT: Math.round(netVAT * 100) / 100,
      vatToPay: Math.max(0, Math.round(netVAT * 100) / 100),
      vatToRefund: Math.max(0, Math.round(-netVAT * 100) / 100),
    },
  };
};
