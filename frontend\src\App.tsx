import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';

// Context Providers
import { AuthProvider } from '@/contexts/AuthContext';
import { ThemeProvider } from '@/contexts/ThemeContext';

// Components
import Layout from '@/components/Layout/Layout';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import ErrorBoundary from '@/components/ErrorBoundary';

// Pages
import Login from '@/pages/Auth/Login';
import Register from '@/pages/Auth/Register';
import Dashboard from '@/pages/Dashboard/Dashboard';
import Sales from '@/pages/Sales/Sales';
import Purchases from '@/pages/Purchases/Purchases';
import Inventory from '@/pages/Inventory/Inventory';
import Customers from '@/pages/Customers/Customers';
import Suppliers from '@/pages/Suppliers/Suppliers';
import Invoices from '@/pages/Invoices/Invoices';
import Payments from '@/pages/Payments/Payments';
import VAT from '@/pages/VAT/VAT';
import Employees from '@/pages/Employees/Employees';
import Payroll from '@/pages/Payroll/Payroll';
import Assets from '@/pages/Assets/Assets';
import Reports from '@/pages/Reports/Reports';
import Settings from '@/pages/Settings/Settings';

// Hooks
import { useAuth } from '@/hooks/useAuth';

// Styles
import './App.css';
import './i18n';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// Public Route Component (redirect if authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

function App() {
  const { i18n } = useTranslation();

  // Set document direction based on language
  React.useEffect(() => {
    document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = i18n.language;
  }, [i18n.language]);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <AuthProvider>
            <Router>
              <div className="App min-h-screen bg-gray-50 dark:bg-gray-900">
                <Suspense fallback={<LoadingSpinner />}>
                  <Routes>
                    {/* Public Routes */}
                    <Route
                      path="/login"
                      element={
                        <PublicRoute>
                          <Login />
                        </PublicRoute>
                      }
                    />
                    <Route
                      path="/register"
                      element={
                        <PublicRoute>
                          <Register />
                        </PublicRoute>
                      }
                    />

                    {/* Protected Routes */}
                    <Route
                      path="/*"
                      element={
                        <ProtectedRoute>
                          <Layout>
                            <Routes>
                              <Route path="/" element={<Navigate to="/dashboard" replace />} />
                              <Route path="/dashboard" element={<Dashboard />} />
                              
                              {/* Sales Module */}
                              <Route path="/sales/*" element={<Sales />} />
                              
                              {/* Purchases Module */}
                              <Route path="/purchases/*" element={<Purchases />} />
                              
                              {/* Inventory Module */}
                              <Route path="/inventory/*" element={<Inventory />} />
                              
                              {/* Customers Module */}
                              <Route path="/customers/*" element={<Customers />} />
                              
                              {/* Suppliers Module */}
                              <Route path="/suppliers/*" element={<Suppliers />} />
                              
                              {/* Invoices Module */}
                              <Route path="/invoices/*" element={<Invoices />} />
                              
                              {/* Payments Module */}
                              <Route path="/payments/*" element={<Payments />} />
                              
                              {/* VAT Module */}
                              <Route path="/vat/*" element={<VAT />} />
                              
                              {/* HR Modules */}
                              <Route path="/employees/*" element={<Employees />} />
                              <Route path="/payroll/*" element={<Payroll />} />
                              
                              {/* Assets Module */}
                              <Route path="/assets/*" element={<Assets />} />
                              
                              {/* Reports Module */}
                              <Route path="/reports/*" element={<Reports />} />
                              
                              {/* Settings Module */}
                              <Route path="/settings/*" element={<Settings />} />
                              
                              {/* 404 Route */}
                              <Route path="*" element={<Navigate to="/dashboard" replace />} />
                            </Routes>
                          </Layout>
                        </ProtectedRoute>
                      }
                    />
                  </Routes>
                </Suspense>

                {/* Toast Notifications */}
                <Toaster
                  position={i18n.language === 'ar' ? 'top-left' : 'top-right'}
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: '#363636',
                      color: '#fff',
                      fontFamily: i18n.language === 'ar' ? 'Noto Sans Arabic' : 'Inter',
                    },
                    success: {
                      style: {
                        background: '#22c55e',
                      },
                    },
                    error: {
                      style: {
                        background: '#ef4444',
                      },
                    },
                  }}
                />
              </div>
            </Router>
          </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
