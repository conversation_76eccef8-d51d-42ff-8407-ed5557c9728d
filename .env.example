# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/saudi_erp_db"

# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-secret-key-here
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Saudi ERP System"

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# ZATCA (Saudi Tax Authority) Configuration
ZATCA_API_URL=https://api.zatca.gov.sa
ZATCA_CLIENT_ID=your-zatca-client-id
ZATCA_CLIENT_SECRET=your-zatca-client-secret
ZATCA_ENVIRONMENT=sandbox

# E-Invoice Configuration
EINVOICE_API_URL=https://einvoice.zatca.gov.sa
EINVOICE_USERNAME=your-einvoice-username
EINVOICE_PASSWORD=your-einvoice-password

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Backup Configuration
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30

# Company Default Settings
DEFAULT_VAT_RATE=15
DEFAULT_CURRENCY=SAR
DEFAULT_LANGUAGE=ar
DEFAULT_TIMEZONE=Asia/Riyadh

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-here
CORS_ORIGIN=http://localhost:3001

# Payment Gateway (Optional)
PAYMENT_GATEWAY_URL=
PAYMENT_GATEWAY_KEY=
PAYMENT_GATEWAY_SECRET=
