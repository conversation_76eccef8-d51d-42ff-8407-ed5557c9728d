import { prisma } from '../server';
import { VATReturnData, ZATCAResponse } from '@/types';
import { calculateVAT, generateVATSummary } from '@/utils/vat';
import axios from 'axios';

export class VATService {
  /**
   * Calculate VAT return for a specific period
   */
  static async calculateVATReturn(
    companyId: string,
    periodStart: Date,
    periodEnd: Date
  ): Promise<VATReturnData> {
    // Get all sales in the period
    const sales = await prisma.sale.findMany({
      where: {
        companyId,
        date: {
          gte: periodStart,
          lte: periodEnd,
        },
        status: { not: 'CANCELLED' },
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    // Get all purchases in the period
    const purchases = await prisma.purchase.findMany({
      where: {
        companyId,
        date: {
          gte: periodStart,
          lte: periodEnd,
        },
        status: { not: 'CANCELLED' },
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    // Calculate totals
    let totalSales = 0;
    let outputVAT = 0;
    let totalPurchases = 0;
    let inputVAT = 0;

    // Process sales
    sales.forEach(sale => {
      totalSales += Number(sale.subtotal);
      outputVAT += Number(sale.vatAmount);
    });

    // Process purchases
    purchases.forEach(purchase => {
      totalPurchases += Number(purchase.subtotal);
      inputVAT += Number(purchase.vatAmount);
    });

    const netVAT = outputVAT - inputVAT;

    return {
      periodStart,
      periodEnd,
      totalSales: Math.round(totalSales * 100) / 100,
      totalPurchases: Math.round(totalPurchases * 100) / 100,
      outputVAT: Math.round(outputVAT * 100) / 100,
      inputVAT: Math.round(inputVAT * 100) / 100,
      netVAT: Math.round(netVAT * 100) / 100,
    };
  }

  /**
   * Create VAT return record
   */
  static async createVATReturn(
    companyId: string,
    vatReturnData: VATReturnData
  ) {
    return await prisma.vATReturn.create({
      data: {
        companyId,
        ...vatReturnData,
        status: 'DRAFT',
      },
    });
  }

  /**
   * Submit VAT return to ZATCA
   */
  static async submitVATReturn(vatReturnId: string): Promise<ZATCAResponse> {
    try {
      const vatReturn = await prisma.vATReturn.findUnique({
        where: { id: vatReturnId },
        include: {
          company: true,
        },
      });

      if (!vatReturn) {
        throw new Error('VAT return not found');
      }

      if (vatReturn.status !== 'DRAFT') {
        throw new Error('VAT return has already been submitted');
      }

      // Prepare ZATCA submission data
      const submissionData = {
        companyVATNumber: vatReturn.company.vatNumber,
        periodStart: vatReturn.periodStart.toISOString(),
        periodEnd: vatReturn.periodEnd.toISOString(),
        totalSales: vatReturn.totalSales,
        totalPurchases: vatReturn.totalPurchases,
        outputVAT: vatReturn.outputVAT,
        inputVAT: vatReturn.inputVAT,
        netVAT: vatReturn.netVAT,
      };

      // Submit to ZATCA API (mock implementation)
      const response = await this.submitToZATCA(submissionData);

      if (response.success) {
        // Update VAT return status
        await prisma.vATReturn.update({
          where: { id: vatReturnId },
          data: {
            status: 'SUBMITTED',
            submittedAt: new Date(),
            zatcaReference: response.uuid,
          },
        });
      }

      return response;
    } catch (error) {
      console.error('Error submitting VAT return:', error);
      throw error;
    }
  }

  /**
   * Submit to ZATCA API (mock implementation)
   */
  private static async submitToZATCA(data: any): Promise<ZATCAResponse> {
    try {
      // This is a mock implementation
      // In production, you would integrate with the actual ZATCA API
      
      const zatcaApiUrl = process.env.ZATCA_API_URL;
      const clientId = process.env.ZATCA_CLIENT_ID;
      const clientSecret = process.env.ZATCA_CLIENT_SECRET;

      if (!zatcaApiUrl || !clientId || !clientSecret) {
        // Mock response for development
        return {
          success: true,
          uuid: `ZATCA-${Date.now()}`,
          hash: `hash-${Math.random().toString(36).substr(2, 9)}`,
        };
      }

      // Actual ZATCA API call would go here
      const response = await axios.post(`${zatcaApiUrl}/vat-returns`, data, {
        headers: {
          'Authorization': `Bearer ${await this.getZATCAToken()}`,
          'Content-Type': 'application/json',
        },
      });

      return {
        success: true,
        uuid: response.data.uuid,
        hash: response.data.hash,
      };
    } catch (error: any) {
      console.error('ZATCA API Error:', error);
      return {
        success: false,
        errors: [error.response?.data?.message || 'ZATCA submission failed'],
      };
    }
  }

  /**
   * Get ZATCA authentication token
   */
  private static async getZATCAToken(): Promise<string> {
    try {
      const response = await axios.post(`${process.env.ZATCA_API_URL}/auth/token`, {
        client_id: process.env.ZATCA_CLIENT_ID,
        client_secret: process.env.ZATCA_CLIENT_SECRET,
        grant_type: 'client_credentials',
      });

      return response.data.access_token;
    } catch (error) {
      console.error('Error getting ZATCA token:', error);
      throw new Error('Failed to authenticate with ZATCA');
    }
  }

  /**
   * Get VAT summary for dashboard
   */
  static async getVATSummary(companyId: string, year: number, month?: number) {
    const startDate = month 
      ? new Date(year, month - 1, 1)
      : new Date(year, 0, 1);
    
    const endDate = month
      ? new Date(year, month, 0)
      : new Date(year, 11, 31);

    // Get sales data
    const salesData = await prisma.sale.findMany({
      where: {
        companyId,
        date: {
          gte: startDate,
          lte: endDate,
        },
        status: { not: 'CANCELLED' },
      },
      select: {
        subtotal: true,
        vatAmount: true,
        total: true,
      },
    });

    // Get purchases data
    const purchasesData = await prisma.purchase.findMany({
      where: {
        companyId,
        date: {
          gte: startDate,
          lte: endDate,
        },
        status: { not: 'CANCELLED' },
      },
      select: {
        subtotal: true,
        vatAmount: true,
        total: true,
      },
    });

    // Transform data for VAT summary
    const transactions = [
      ...salesData.map(sale => ({
        subtotal: Number(sale.subtotal),
        vatAmount: Number(sale.vatAmount),
        total: Number(sale.total),
        type: 'SALE' as const,
      })),
      ...purchasesData.map(purchase => ({
        subtotal: Number(purchase.subtotal),
        vatAmount: Number(purchase.vatAmount),
        total: Number(purchase.total),
        type: 'PURCHASE' as const,
      })),
    ];

    return generateVATSummary(transactions);
  }

  /**
   * Validate VAT number format
   */
  static validateVATNumber(vatNumber: string): boolean {
    // Saudi VAT number format: 3XXXXXXXXXX3 (15 digits starting and ending with 3)
    const vatRegex = /^3[0-9]{13}3$/;
    return vatRegex.test(vatNumber);
  }

  /**
   * Get VAT rate by product category
   */
  static getVATRateByCategory(category: string): number {
    const exemptCategories = [
      'MEDICAL',
      'EDUCATION', 
      'FOOD_BASIC',
      'REAL_ESTATE_RESIDENTIAL',
    ];

    const zeroRatedCategories = [
      'EXPORTS',
      'INTERNATIONAL_TRANSPORT',
      'PRECIOUS_METALS',
    ];

    if (exemptCategories.includes(category.toUpperCase())) {
      return 0; // Exempt
    }

    if (zeroRatedCategories.includes(category.toUpperCase())) {
      return 0; // Zero-rated
    }

    return 15; // Standard rate
  }

  /**
   * Generate VAT report
   */
  static async generateVATReport(
    companyId: string,
    periodStart: Date,
    periodEnd: Date
  ) {
    const vatReturn = await this.calculateVATReturn(companyId, periodStart, periodEnd);
    
    // Get detailed breakdown
    const salesByCategory = await prisma.sale.groupBy({
      by: ['status'],
      where: {
        companyId,
        date: {
          gte: periodStart,
          lte: periodEnd,
        },
      },
      _sum: {
        subtotal: true,
        vatAmount: true,
        total: true,
      },
      _count: true,
    });

    const purchasesByCategory = await prisma.purchase.groupBy({
      by: ['status'],
      where: {
        companyId,
        date: {
          gte: periodStart,
          lte: periodEnd,
        },
      },
      _sum: {
        subtotal: true,
        vatAmount: true,
        total: true,
      },
      _count: true,
    });

    return {
      summary: vatReturn,
      breakdown: {
        sales: salesByCategory,
        purchases: purchasesByCategory,
      },
      generatedAt: new Date(),
    };
  }
}
