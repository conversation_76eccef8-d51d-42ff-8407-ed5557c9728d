version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: saudi_erp_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: saudi_erp_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - saudi_erp_network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: saudi_erp_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - saudi_erp_network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: saudi_erp_backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: ***********************************************/saudi_erp_db
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-here
      PORT: 3000
    ports:
      - "3000:3000"
    volumes:
      - ./src:/app/src
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - saudi_erp_network

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: saudi_erp_frontend
    restart: unless-stopped
    ports:
      - "3001:3001"
    volumes:
      - ./frontend/src:/app/src
    depends_on:
      - backend
    networks:
      - saudi_erp_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: saudi_erp_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads
    depends_on:
      - backend
      - frontend
    networks:
      - saudi_erp_network

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: saudi_erp_backup
    restart: "no"
    environment:
      PGPASSWORD: postgres123
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    command: /bin/sh -c "chmod +x /backup.sh && /backup.sh"
    depends_on:
      - postgres
    networks:
      - saudi_erp_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  saudi_erp_network:
    driver: bridge
