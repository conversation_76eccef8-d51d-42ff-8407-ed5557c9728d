// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ===== User Management =====
model User {
  id                String   @id @default(uuid())
  email             String   @unique
  username          String   @unique
  password          String
  firstName         String
  lastName          String
  phone             String?
  isActive          Boolean  @default(true)
  role              UserRole @default(USER)
  lastLogin         DateTime?
  emailVerified     <PERSON>ole<PERSON>  @default(false)
  twoFactorEnabled  Boolean  @default(false)
  twoFactorSecret   String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  company           Company? @relation(fields: [companyId], references: [id])
  companyId         String?
  createdSales      Sale[]
  createdPurchases  Purchase[]
  createdInvoices   Invoice[]
  auditLogs         AuditLog[]

  @@map("users")
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  MANAGER
  ACCOUNTANT
  SALES_REP
  WAREHOUSE_KEEPER
  USER
}

// ===== Company Management =====
model Company {
  id                    String   @id @default(uuid())
  nameAr                String
  nameEn                String
  commercialRegister    String   @unique
  taxNumber             String   @unique
  vatNumber             String?  @unique
  phone                 String
  email                 String
  website               String?
  addressAr             String
  addressEn             String
  city                  String
  postalCode            String
  country               String   @default("SA")
  isActive              Boolean  @default(true)
  subscriptionPlan      SubscriptionPlan @default(BASIC)
  subscriptionExpiry    DateTime?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  users                 User[]
  customers             Customer[]
  suppliers             Supplier[]
  products              Product[]
  sales                 Sale[]
  purchases             Purchase[]
  invoices              Invoice[]
  vatReturns            VATReturn[]
  employees             Employee[]
  fixedAssets           FixedAsset[]

  @@map("companies")
}

enum SubscriptionPlan {
  BASIC
  PROFESSIONAL
  ENTERPRISE
}

// ===== Customer Management =====
model Customer {
  id                String   @id @default(uuid())
  code              String   @unique
  nameAr            String
  nameEn            String?
  email             String?
  phone             String
  mobile            String?
  taxNumber         String?
  vatNumber         String?
  addressAr         String
  addressEn         String?
  city              String
  postalCode        String?
  country           String   @default("SA")
  creditLimit       Decimal  @default(0) @db.Decimal(15, 2)
  paymentTerms      Int      @default(30) // days
  isActive          Boolean  @default(true)
  customerType      CustomerType @default(INDIVIDUAL)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  company           Company  @relation(fields: [companyId], references: [id])
  companyId         String
  sales             Sale[]
  invoices          Invoice[]

  @@map("customers")
}

enum CustomerType {
  INDIVIDUAL
  COMPANY
  GOVERNMENT
}

// ===== Supplier Management =====
model Supplier {
  id                String   @id @default(uuid())
  code              String   @unique
  nameAr            String
  nameEn            String?
  email             String?
  phone             String
  mobile            String?
  taxNumber         String?
  vatNumber         String?
  addressAr         String
  addressEn         String?
  city              String
  postalCode        String?
  country           String   @default("SA")
  paymentTerms      Int      @default(30) // days
  isActive          Boolean  @default(true)
  supplierType      SupplierType @default(LOCAL)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  company           Company  @relation(fields: [companyId], references: [id])
  companyId         String
  purchases         Purchase[]
  products          Product[]

  @@map("suppliers")
}

enum SupplierType {
  LOCAL
  INTERNATIONAL
}

// ===== Product Management =====
model Product {
  id                String   @id @default(uuid())
  code              String   @unique
  nameAr            String
  nameEn            String?
  description       String?
  barcode           String?  @unique
  category          String
  unit              String
  costPrice         Decimal  @db.Decimal(15, 2)
  sellingPrice      Decimal  @db.Decimal(15, 2)
  vatRate           Decimal  @default(15) @db.Decimal(5, 2)
  minStock          Int      @default(0)
  maxStock          Int?
  currentStock      Int      @default(0)
  isActive          Boolean  @default(true)
  isVatExempt       Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  company           Company  @relation(fields: [companyId], references: [id])
  companyId         String
  supplier          Supplier? @relation(fields: [supplierId], references: [id])
  supplierId        String?
  saleItems         SaleItem[]
  purchaseItems     PurchaseItem[]
  invoiceItems      InvoiceItem[]
  stockMovements    StockMovement[]

  @@map("products")
}

// ===== Sales Management =====
model Sale {
  id                String   @id @default(uuid())
  saleNumber        String   @unique
  date              DateTime @default(now())
  customerId        String
  subtotal          Decimal  @db.Decimal(15, 2)
  vatAmount         Decimal  @db.Decimal(15, 2)
  total             Decimal  @db.Decimal(15, 2)
  discount          Decimal  @default(0) @db.Decimal(15, 2)
  status            SaleStatus @default(DRAFT)
  notes             String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  company           Company  @relation(fields: [companyId], references: [id])
  companyId         String
  customer          Customer @relation(fields: [customerId], references: [id])
  createdBy         User     @relation(fields: [createdById], references: [id])
  createdById       String
  items             SaleItem[]
  invoice           Invoice?

  @@map("sales")
}

model SaleItem {
  id                String   @id @default(uuid())
  saleId            String
  productId         String
  quantity          Int
  unitPrice         Decimal  @db.Decimal(15, 2)
  vatRate           Decimal  @db.Decimal(5, 2)
  vatAmount         Decimal  @db.Decimal(15, 2)
  total             Decimal  @db.Decimal(15, 2)

  // Relations
  sale              Sale     @relation(fields: [saleId], references: [id], onDelete: Cascade)
  product           Product  @relation(fields: [productId], references: [id])

  @@map("sale_items")
}

enum SaleStatus {
  DRAFT
  CONFIRMED
  INVOICED
  CANCELLED
}

// ===== Purchase Management =====
model Purchase {
  id                String   @id @default(uuid())
  purchaseNumber    String   @unique
  date              DateTime @default(now())
  supplierId        String
  subtotal          Decimal  @db.Decimal(15, 2)
  vatAmount         Decimal  @db.Decimal(15, 2)
  total             Decimal  @db.Decimal(15, 2)
  discount          Decimal  @default(0) @db.Decimal(15, 2)
  status            PurchaseStatus @default(DRAFT)
  notes             String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  company           Company  @relation(fields: [companyId], references: [id])
  companyId         String
  supplier          Supplier @relation(fields: [supplierId], references: [id])
  createdBy         User     @relation(fields: [createdById], references: [id])
  createdById       String
  items             PurchaseItem[]

  @@map("purchases")
}

model PurchaseItem {
  id                String   @id @default(uuid())
  purchaseId        String
  productId         String
  quantity          Int
  unitPrice         Decimal  @db.Decimal(15, 2)
  vatRate           Decimal  @db.Decimal(5, 2)
  vatAmount         Decimal  @db.Decimal(15, 2)
  total             Decimal  @db.Decimal(15, 2)

  // Relations
  purchase          Purchase @relation(fields: [purchaseId], references: [id], onDelete: Cascade)
  product           Product  @relation(fields: [productId], references: [id])

  @@map("purchase_items")
}

enum PurchaseStatus {
  DRAFT
  CONFIRMED
  RECEIVED
  CANCELLED
}

// ===== Invoice Management =====
model Invoice {
  id                String   @id @default(uuid())
  invoiceNumber     String   @unique
  date              DateTime @default(now())
  dueDate           DateTime
  customerId        String
  saleId            String?  @unique
  subtotal          Decimal  @db.Decimal(15, 2)
  vatAmount         Decimal  @db.Decimal(15, 2)
  total             Decimal  @db.Decimal(15, 2)
  discount          Decimal  @default(0) @db.Decimal(15, 2)
  paidAmount        Decimal  @default(0) @db.Decimal(15, 2)
  status            InvoiceStatus @default(DRAFT)
  paymentStatus     PaymentStatus @default(UNPAID)
  isEInvoice        Boolean  @default(false)
  eInvoiceUUID      String?  @unique
  qrCode            String?
  zatcaHash         String?
  notes             String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  company           Company  @relation(fields: [companyId], references: [id])
  companyId         String
  customer          Customer @relation(fields: [customerId], references: [id])
  sale              Sale?    @relation(fields: [saleId], references: [id])
  createdBy         User     @relation(fields: [createdById], references: [id])
  createdById       String
  items             InvoiceItem[]
  payments          Payment[]

  @@map("invoices")
}

model InvoiceItem {
  id                String   @id @default(uuid())
  invoiceId         String
  productId         String
  description       String
  quantity          Int
  unitPrice         Decimal  @db.Decimal(15, 2)
  vatRate           Decimal  @db.Decimal(5, 2)
  vatAmount         Decimal  @db.Decimal(15, 2)
  total             Decimal  @db.Decimal(15, 2)

  // Relations
  invoice           Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  product           Product  @relation(fields: [productId], references: [id])

  @@map("invoice_items")
}

enum InvoiceStatus {
  DRAFT
  SENT
  APPROVED
  CANCELLED
}

enum PaymentStatus {
  UNPAID
  PARTIALLY_PAID
  PAID
  OVERDUE
}

// ===== Payment Management =====
model Payment {
  id                String   @id @default(uuid())
  invoiceId         String
  amount            Decimal  @db.Decimal(15, 2)
  paymentDate       DateTime @default(now())
  paymentMethod     PaymentMethod
  reference         String?
  notes             String?
  createdAt         DateTime @default(now())

  // Relations
  invoice           Invoice  @relation(fields: [invoiceId], references: [id])

  @@map("payments")
}

enum PaymentMethod {
  CASH
  BANK_TRANSFER
  CREDIT_CARD
  CHEQUE
  OTHER
}

// ===== Stock Management =====
model StockMovement {
  id                String   @id @default(uuid())
  productId         String
  movementType      MovementType
  quantity          Int
  unitCost          Decimal? @db.Decimal(15, 2)
  reference         String?
  notes             String?
  date              DateTime @default(now())

  // Relations
  product           Product  @relation(fields: [productId], references: [id])

  @@map("stock_movements")
}

enum MovementType {
  IN
  OUT
  ADJUSTMENT
  TRANSFER
}

// ===== VAT Management =====
model VATReturn {
  id                String   @id @default(uuid())
  periodStart       DateTime
  periodEnd         DateTime
  totalSales        Decimal  @db.Decimal(15, 2)
  totalPurchases    Decimal  @db.Decimal(15, 2)
  outputVAT         Decimal  @db.Decimal(15, 2)
  inputVAT          Decimal  @db.Decimal(15, 2)
  netVAT            Decimal  @db.Decimal(15, 2)
  status            VATReturnStatus @default(DRAFT)
  submittedAt       DateTime?
  zatcaReference    String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  company           Company  @relation(fields: [companyId], references: [id])
  companyId         String

  @@map("vat_returns")
}

enum VATReturnStatus {
  DRAFT
  SUBMITTED
  APPROVED
  REJECTED
}

// ===== Employee Management =====
model Employee {
  id                String   @id @default(uuid())
  employeeNumber    String   @unique
  firstNameAr       String
  lastNameAr        String
  firstNameEn       String?
  lastNameEn        String?
  nationalId        String   @unique
  iqamaNumber       String?
  email             String?
  phone             String
  birthDate         DateTime
  hireDate          DateTime
  department        String
  position          String
  basicSalary       Decimal  @db.Decimal(15, 2)
  allowances        Decimal  @default(0) @db.Decimal(15, 2)
  isActive          Boolean  @default(true)
  bankAccount       String?
  iban              String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  company           Company  @relation(fields: [companyId], references: [id])
  companyId         String
  payrolls          Payroll[]

  @@map("employees")
}

// ===== Payroll Management =====
model Payroll {
  id                String   @id @default(uuid())
  employeeId        String
  month             Int
  year              Int
  basicSalary       Decimal  @db.Decimal(15, 2)
  allowances        Decimal  @default(0) @db.Decimal(15, 2)
  overtime          Decimal  @default(0) @db.Decimal(15, 2)
  deductions        Decimal  @default(0) @db.Decimal(15, 2)
  gosi              Decimal  @default(0) @db.Decimal(15, 2)
  netSalary         Decimal  @db.Decimal(15, 2)
  status            PayrollStatus @default(DRAFT)
  paidAt            DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  employee          Employee @relation(fields: [employeeId], references: [id])

  @@unique([employeeId, month, year])
  @@map("payrolls")
}

enum PayrollStatus {
  DRAFT
  APPROVED
  PAID
}

// ===== Fixed Assets Management =====
model FixedAsset {
  id                String   @id @default(uuid())
  assetNumber       String   @unique
  nameAr            String
  nameEn            String?
  category          String
  purchaseDate      DateTime
  purchasePrice     Decimal  @db.Decimal(15, 2)
  currentValue      Decimal  @db.Decimal(15, 2)
  depreciationRate  Decimal  @db.Decimal(5, 2)
  usefulLife        Int      // in years
  location          String?
  condition         AssetCondition @default(GOOD)
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  company           Company  @relation(fields: [companyId], references: [id])
  companyId         String
  depreciations     Depreciation[]

  @@map("fixed_assets")
}

model Depreciation {
  id                String   @id @default(uuid())
  assetId           String
  year              Int
  month             Int
  amount            Decimal  @db.Decimal(15, 2)
  accumulatedAmount Decimal  @db.Decimal(15, 2)
  createdAt         DateTime @default(now())

  // Relations
  asset             FixedAsset @relation(fields: [assetId], references: [id])

  @@unique([assetId, year, month])
  @@map("depreciations")
}

enum AssetCondition {
  EXCELLENT
  GOOD
  FAIR
  POOR
  DAMAGED
}

// ===== Audit Log =====
model AuditLog {
  id                String   @id @default(uuid())
  userId            String
  action            String
  tableName         String
  recordId          String
  oldValues         Json?
  newValues         Json?
  ipAddress         String?
  userAgent         String?
  createdAt         DateTime @default(now())

  // Relations
  user              User     @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}
