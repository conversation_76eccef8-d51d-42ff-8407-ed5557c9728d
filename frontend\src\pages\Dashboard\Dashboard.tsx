import React from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Users,
  Package,
  FileText,
  AlertTriangle,
  DollarSign,
  ShoppingCart,
  CreditCard,
  Building,
} from 'lucide-react';

// Components
import DashboardCard from '@/components/Dashboard/DashboardCard';
import RecentTransactions from '@/components/Dashboard/RecentTransactions';
import TopProducts from '@/components/Dashboard/TopProducts';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import ErrorMessage from '@/components/UI/ErrorMessage';

// Services
import { dashboardAPI } from '@/services/api';

// Types
interface DashboardStats {
  overview: {
    totalCustomers: number;
    totalSuppliers: number;
    totalProducts: number;
    totalEmployees: number;
  };
  monthly: {
    sales: {
      amount: number;
      count: number;
    };
    purchases: {
      amount: number;
      count: number;
    };
  };
  alerts: {
    pendingInvoices: {
      amount: number;
      count: number;
    };
    lowStockProducts: number;
  };
}

interface SalesData {
  date: string;
  sales: number;
  purchases: number;
}

const Dashboard: React.FC = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  // Fetch dashboard data
  const {
    data: statsData,
    isLoading: statsLoading,
    error: statsError,
  } = useQuery('dashboard-stats', dashboardAPI.getStats);

  const {
    data: salesChartData,
    isLoading: chartLoading,
  } = useQuery('dashboard-sales-chart', () => 
    dashboardAPI.getSalesChart({ period: '30days' })
  );

  const {
    data: recentTransactions,
    isLoading: transactionsLoading,
  } = useQuery('dashboard-recent-transactions', () =>
    dashboardAPI.getRecentTransactions({ limit: 10 })
  );

  const {
    data: topProducts,
    isLoading: productsLoading,
  } = useQuery('dashboard-top-products', () =>
    dashboardAPI.getTopProducts({ limit: 5 })
  );

  if (statsLoading) {
    return <LoadingSpinner />;
  }

  if (statsError) {
    return <ErrorMessage message={t('dashboard.error.loadingFailed')} />;
  }

  const stats: DashboardStats = statsData?.data?.stats || {
    overview: { totalCustomers: 0, totalSuppliers: 0, totalProducts: 0, totalEmployees: 0 },
    monthly: { sales: { amount: 0, count: 0 }, purchases: { amount: 0, count: 0 } },
    alerts: { pendingInvoices: { amount: 0, count: 0 }, lowStockProducts: 0 },
  };

  // Chart colors
  const COLORS = ['#0ea5e9', '#22c55e', '#f59e0b', '#ef4444'];

  // Prepare pie chart data
  const pieData = [
    { name: t('dashboard.charts.sales'), value: stats.monthly.sales.amount },
    { name: t('dashboard.charts.purchases'), value: stats.monthly.purchases.amount },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('dashboard.title')}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {t('dashboard.subtitle')}
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {new Date().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </span>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <DashboardCard
          title={t('dashboard.cards.totalSales')}
          value={`${stats.monthly.sales.amount.toLocaleString()} ${t('common.currency')}`}
          change={`+${stats.monthly.sales.count} ${t('dashboard.cards.transactions')}`}
          changeType="positive"
          icon={<DollarSign className="h-6 w-6" />}
          color="blue"
        />
        
        <DashboardCard
          title={t('dashboard.cards.totalPurchases')}
          value={`${stats.monthly.purchases.amount.toLocaleString()} ${t('common.currency')}`}
          change={`+${stats.monthly.purchases.count} ${t('dashboard.cards.transactions')}`}
          changeType="neutral"
          icon={<ShoppingCart className="h-6 w-6" />}
          color="green"
        />
        
        <DashboardCard
          title={t('dashboard.cards.pendingInvoices')}
          value={`${stats.alerts.pendingInvoices.amount.toLocaleString()} ${t('common.currency')}`}
          change={`${stats.alerts.pendingInvoices.count} ${t('dashboard.cards.invoices')}`}
          changeType="negative"
          icon={<FileText className="h-6 w-6" />}
          color="yellow"
        />
        
        <DashboardCard
          title={t('dashboard.cards.lowStock')}
          value={stats.alerts.lowStockProducts.toString()}
          change={t('dashboard.cards.needsAttention')}
          changeType="negative"
          icon={<AlertTriangle className="h-6 w-6" />}
          color="red"
        />
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <DashboardCard
          title={t('dashboard.overview.customers')}
          value={stats.overview.totalCustomers.toString()}
          icon={<Users className="h-6 w-6" />}
          color="blue"
          simple
        />
        
        <DashboardCard
          title={t('dashboard.overview.suppliers')}
          value={stats.overview.totalSuppliers.toString()}
          icon={<Building className="h-6 w-6" />}
          color="green"
          simple
        />
        
        <DashboardCard
          title={t('dashboard.overview.products')}
          value={stats.overview.totalProducts.toString()}
          icon={<Package className="h-6 w-6" />}
          color="purple"
          simple
        />
        
        <DashboardCard
          title={t('dashboard.overview.employees')}
          value={stats.overview.totalEmployees.toString()}
          icon={<Users className="h-6 w-6" />}
          color="indigo"
          simple
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Sales Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {t('dashboard.charts.salesTrend')}
          </h3>
          {chartLoading ? (
            <div className="h-64 flex items-center justify-center">
              <LoadingSpinner size="sm" />
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={salesChartData?.data || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => new Date(value).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', { month: 'short', day: 'numeric' })}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                  formatter={(value: number) => [`${value.toLocaleString()} ${t('common.currency')}`, '']}
                />
                <Line 
                  type="monotone" 
                  dataKey="sales" 
                  stroke="#0ea5e9" 
                  strokeWidth={2}
                  name={t('dashboard.charts.sales')}
                />
                <Line 
                  type="monotone" 
                  dataKey="purchases" 
                  stroke="#22c55e" 
                  strokeWidth={2}
                  name={t('dashboard.charts.purchases')}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>

        {/* Revenue Distribution */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {t('dashboard.charts.revenueDistribution')}
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value: number) => `${value.toLocaleString()} ${t('common.currency')}`} />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Transactions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('dashboard.recent.transactions')}
            </h3>
          </div>
          <div className="p-6">
            {transactionsLoading ? (
              <LoadingSpinner size="sm" />
            ) : (
              <RecentTransactions data={recentTransactions?.data || []} />
            )}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('dashboard.recent.topProducts')}
            </h3>
          </div>
          <div className="p-6">
            {productsLoading ? (
              <LoadingSpinner size="sm" />
            ) : (
              <TopProducts data={topProducts?.data || []} />
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {t('dashboard.quickActions.title')}
        </h3>
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
          <button className="flex flex-col items-center p-4 text-center border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <FileText className="h-8 w-8 text-blue-500 mb-2" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {t('dashboard.quickActions.newInvoice')}
            </span>
          </button>
          
          <button className="flex flex-col items-center p-4 text-center border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <ShoppingCart className="h-8 w-8 text-green-500 mb-2" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {t('dashboard.quickActions.newSale')}
            </span>
          </button>
          
          <button className="flex flex-col items-center p-4 text-center border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <Package className="h-8 w-8 text-purple-500 mb-2" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {t('dashboard.quickActions.addProduct')}
            </span>
          </button>
          
          <button className="flex flex-col items-center p-4 text-center border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <Users className="h-8 w-8 text-indigo-500 mb-2" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {t('dashboard.quickActions.addCustomer')}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
