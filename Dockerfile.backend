# نظام ERP السعودي - Backend Dockerfile
# Saudi ERP System - Backend Dockerfile

# استخدام Node.js 18 Alpine كصورة أساسية
FROM node:18-alpine

# تعيين مجلد العمل
WORKDIR /app

# تثبيت التبعيات الأساسية
RUN apk add --no-cache \
    postgresql-client \
    curl \
    bash

# نسخ ملفات package
COPY package*.json ./
COPY tsconfig.json ./

# تثبيت التبعيات
RUN npm ci --only=production && npm cache clean --force

# نسخ ملفات المشروع
COPY src/ ./src/
COPY prisma/ ./prisma/

# إنشاء مجلدات مطلوبة
RUN mkdir -p logs uploads

# بناء المشروع
RUN npm run build

# إنشاء مستخدم غير جذر
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# تغيير ملكية الملفات
RUN chown -R nodejs:nodejs /app
USER nodejs

# كشف المنفذ
EXPOSE 3000

# فحص صحة الحاوية
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# أمر التشغيل
CMD ["npm", "start"]
