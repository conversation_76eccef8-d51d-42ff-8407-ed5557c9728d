# نظام ERP السعودي - Frontend Dockerfile
# Saudi ERP System - Frontend Dockerfile

# مرحلة البناء
FROM node:18-alpine as builder

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات package
COPY package*.json ./

# تثبيت التبعيات
RUN npm ci --only=production && npm cache clean --force

# نسخ ملفات المشروع
COPY . .

# بناء التطبيق
RUN npm run build

# مرحلة الإنتاج
FROM nginx:alpine

# نسخ ملفات البناء
COPY --from=builder /app/dist /usr/share/nginx/html

# نسخ تكوين nginx
COPY nginx.conf /etc/nginx/conf.d/default.conf

# كشف المنفذ
EXPOSE 80

# أمر التشغيل
CMD ["nginx", "-g", "daemon off;"]
