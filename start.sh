#!/bin/bash

# نظام ERP السعودي - سكريبت التشغيل السريع
# Saudi ERP System - Quick Start Script

echo "🇸🇦 مرحباً بك في نظام ERP السعودي المتكامل"
echo "🇸🇦 Welcome to Saudi Integrated ERP System"
echo "=================================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت. يرجى تثبيت Docker أولاً"
    echo "❌ Docker is not installed. Please install Docker first"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose غير مثبت. يرجى تثبيت Docker Compose أولاً"
    echo "❌ Docker Compose is not installed. Please install Docker Compose first"
    exit 1
fi

# Create necessary directories
echo "📁 إنشاء المجلدات المطلوبة..."
echo "📁 Creating necessary directories..."
mkdir -p logs uploads backups

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "⚙️ إنشاء ملف البيئة..."
    echo "⚙️ Creating environment file..."
    cp .env.example .env
    echo "✅ تم إنشاء ملف .env - يرجى تحديث الإعدادات حسب الحاجة"
    echo "✅ .env file created - please update settings as needed"
fi

# Function to start with Docker
start_with_docker() {
    echo "🐳 بدء تشغيل النظام باستخدام Docker..."
    echo "🐳 Starting system with Docker..."
    
    # Pull latest images
    docker-compose pull
    
    # Build and start services
    docker-compose up -d --build
    
    echo "⏳ انتظار تشغيل الخدمات..."
    echo "⏳ Waiting for services to start..."
    sleep 30
    
    # Run database migrations
    echo "🗄️ تطبيق تغييرات قاعدة البيانات..."
    echo "🗄️ Running database migrations..."
    docker-compose exec backend npx prisma migrate deploy
    docker-compose exec backend npx prisma generate
    
    echo "✅ تم تشغيل النظام بنجاح!"
    echo "✅ System started successfully!"
    echo ""
    echo "🌐 الروابط المتاحة | Available URLs:"
    echo "   Frontend: http://localhost:3001"
    echo "   Backend API: http://localhost:3000"
    echo "   Database: localhost:5432"
    echo ""
    echo "📊 لعرض السجلات | To view logs:"
    echo "   docker-compose logs -f"
    echo ""
    echo "🛑 لإيقاف النظام | To stop system:"
    echo "   docker-compose down"
}

# Function to start manually
start_manually() {
    echo "🔧 بدء التشغيل اليدوي..."
    echo "🔧 Starting manually..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js 18+ أولاً"
        echo "❌ Node.js is not installed. Please install Node.js 18+ first"
        exit 1
    fi
    
    # Check if PostgreSQL is running
    if ! pg_isready -h localhost -p 5432 &> /dev/null; then
        echo "❌ PostgreSQL غير متصل. يرجى تشغيل PostgreSQL أولاً"
        echo "❌ PostgreSQL is not running. Please start PostgreSQL first"
        exit 1
    fi
    
    # Install backend dependencies
    echo "📦 تثبيت تبعيات Backend..."
    echo "📦 Installing backend dependencies..."
    npm install
    
    # Run database migrations
    echo "🗄️ تطبيق تغييرات قاعدة البيانات..."
    echo "🗄️ Running database migrations..."
    npx prisma migrate dev
    npx prisma generate
    
    # Start backend in background
    echo "🚀 تشغيل Backend..."
    echo "🚀 Starting backend..."
    npm run dev &
    BACKEND_PID=$!
    
    # Install frontend dependencies
    echo "📦 تثبيت تبعيات Frontend..."
    echo "📦 Installing frontend dependencies..."
    cd frontend
    npm install
    
    # Start frontend
    echo "🎨 تشغيل Frontend..."
    echo "🎨 Starting frontend..."
    npm run dev &
    FRONTEND_PID=$!
    
    cd ..
    
    echo "✅ تم تشغيل النظام بنجاح!"
    echo "✅ System started successfully!"
    echo ""
    echo "🌐 الروابط المتاحة | Available URLs:"
    echo "   Frontend: http://localhost:3001"
    echo "   Backend API: http://localhost:3000"
    echo ""
    echo "🛑 لإيقاف النظام اضغط Ctrl+C"
    echo "🛑 To stop system press Ctrl+C"
    
    # Wait for user to stop
    trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
    wait
}

# Main menu
echo "اختر طريقة التشغيل | Choose startup method:"
echo "1) Docker (موصى به | Recommended)"
echo "2) تشغيل يدوي | Manual setup"
echo "3) إعداد التطوير | Development setup"
echo "4) خروج | Exit"
echo ""
read -p "اختيارك | Your choice (1-4): " choice

case $choice in
    1)
        start_with_docker
        ;;
    2)
        start_manually
        ;;
    3)
        echo "🔧 إعداد بيئة التطوير..."
        echo "🔧 Setting up development environment..."
        
        # Install dependencies
        npm install
        cd frontend && npm install && cd ..
        
        # Setup database
        npx prisma migrate dev
        npx prisma generate
        
        echo "✅ تم إعداد بيئة التطوير!"
        echo "✅ Development environment ready!"
        echo ""
        echo "🚀 لتشغيل Backend: npm run dev"
        echo "🎨 لتشغيل Frontend: cd frontend && npm run dev"
        ;;
    4)
        echo "👋 مع السلامة!"
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ اختيار غير صحيح"
        echo "❌ Invalid choice"
        exit 1
        ;;
esac
